import re
import sqlite3
import tkinter as tk
from datetime import datetime
import pyperclip
from functools import lru_cache
from tkinter import ttk, messagebox, filedialog
import math
from itertools import combinations  # 添加到文件开头的导入部分
import platform
import pytz

last_input_text = ""
is_first_check = [True]
parsed_keys = []
last_saved_data = {}
# 添加全局变量来跟踪屏蔽状态
is_macau_hidden = [False]
is_hongkong_hidden = [False]

# 全局变量
winning_number = None
winning_zodiacs = set()
number_to_zodiac = {}

# 添加全局计数器变量（放在文件开头的全局变量区域）
input_counter = [0]  # 使用列表以便在函数中修改

def update_winning_info(numbers):
    global winning_number, winning_zodiacs, number_to_zodiac
    try:
        if not numbers or len(numbers) != 7:  # 确保输入7个数字
            show_message("请输入7个号码", play_sound=True, sound_type="warning")
            return False
            
        # 最后一个数字作为特码
        winning_number = numbers[-1]
        number_to_zodiac.clear()
        
        # ��保所有号码都能找到对应的生肖
        for num in numbers:
            try:
                num = int(num)
                if not (1 <= num <= 49):
                    show_message(f"号码 {num} 必须在1-49之间", play_sound=True, sound_type="error")
                    return False
                    
                found = False
                for zodiac, nums in ShengXiaoBiao.items():
                    if num in nums:
                        number_to_zodiac[num] = zodiac
                        found = True
                        break
                if not found:
                    show_message(f"找不到号码 {num} 对应的生肖", play_sound=True, sound_type="error")
                    return False
            except ValueError:
                show_message(f"无效的号码格式: {num}", play_sound=True, sound_type="error")
                return False
                
        winning_zodiacs = set(number_to_zodiac[num] for num in numbers)
        show_message("开奖号码设置成功", play_sound=True, sound_type="info")
        return True
        
    except Exception as e:
        show_message(f"更新开奖信息时发生错误: {str(e)}", play_sound=True, sound_type="error")
        return False

# 修改号码输入处理部分
def process_lottery_input(input_text):
    try:
        # 移除所有空白字符和分隔符
        clean_text = re.sub(r'[,，\s]+', ' ', input_text.strip())
        numbers = clean_text.split()
        
        # 修改：允许输入单个特码
        if len(numbers) == 1:
            try:
                special_number = int(numbers[0])
                if not (1 <= special_number <= 49):
                    show_message(f"号码 {special_number} 必须在1-49之间", play_sound=True, sound_type="error")
                    return False
                # 设置全局变量
                global winning_number, winning_zodiacs, number_to_zodiac
                winning_number = special_number
                number_to_zodiac = {special_number: get_zodiac_for_number(special_number)}
                winning_zodiacs = {get_zodiac_for_number(special_number)}
                show_message("特码设置成功", play_sound=True, sound_type="info")
                return True
            except ValueError:
                show_message(f"无效的号码格式: {numbers[0]}", play_sound=True, sound_type="error")
                return False
        
        # 验证号码数量
        if len(numbers) != 7:
            show_message("请输入7个号码", play_sound=True, sound_type="warning")
            return False
            
        # ���换并验证所有号码
        validated_numbers = []
        for num in numbers:
            try:
                n = int(num)
                if not (1 <= n <= 49):
                    show_message(f"号码 {n} 必须在1-49之间", play_sound=True, sound_type="error")
                    return False
                validated_numbers.append(n)
            except ValueError:
                show_message(f"无效的号码格式: {num}", play_sound=True, sound_type="error")
                return False
                
        # 检查是否有重复号码
        if len(set(validated_numbers)) != 7:
            show_message("号码不能重复", play_sound=True, sound_type="error")
            return False
            
        # 更新开奖信息
        return update_winning_info(validated_numbers)
        
    except Exception as e:
        show_message(f"处理开奖号码时发生错误: {str(e)}", play_sound=True, sound_type="error")
        return False

#定义生肖映射
ShengXiaoBiao = {
    '鼠': [5, 17, 29, 41],
    '牛': [4, 16, 28, 40],
    '虎': [3, 15, 27, 39],
    '兔': [2, 14, 26, 38],
    '龙': [1, 13, 25, 37, 49],
    '蛇': [12, 24, 36, 48],
    '马': [11, 23, 35, 47],
    '羊': [10, 22, 34, 46],
    '猴': [9, 21, 33, 45],
    '鸡': [8, 20, 32, 44],
    '狗': [7, 19, 31, 43],
    '猪': [6, 18, 30, 42]}
ShengXiaoList = list(ShengXiaoBiao.keys())

#如果数据库和表不存在，创建数据库和表
def create_database_and_table():
    try:
        conn = sqlite3.connect('注额管理.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS 注额管理 (
                编号 INTEGER,
                额度 INTEGER,
                计数 INTEGER,
                标签 TEXT,
                原始数据 TEXT,
                特殊组合注额 INTEGER,
                特殊组合类型 TEXT,
                标识 TEXT,
                时间戳 TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        print("数据库表创建成功")
    except sqlite3.Error as e:
        print(f"创建数据库错误: {e}")
    finally:
        conn.close()

# 将正则表达式移到全局作用域
DIRECT_PATTERN = re.compile(r'(\d+[:：]\d+)')
# 修改数字匹配模式，使其更灵活
NUMBER_PATTERN = re.compile(r'(\d+(?:[,\s.，\-\/\\、]+\d+)*|\d+到\d+|\d+至\d+|\d+|\w+)')

# 预先构建转换表
CHINESE_DIGITS = {
    '零': 0, '一': 1, '二': 2, '三': 3, '四': 4,
    '五': 5, '六': 6, '七': 7, '八': 8, '九': 9,
    '十': 10, '百': 100, '千': 1000, '万': 10000
}

# 在其他全局函数���前添加（放在 create_database_and_table 函数之前）
def convert_chinese_number(chinese_num):
    """将中文数字转换为阿拉伯数字"""
    if not chinese_num:
        return 1
    return CHINESE_DIGITS.get(chinese_num, 1)

# 使用集合而不是列表来存储分隔符，提高查找效率
DELIMITERS = {'各', '各号', '特各', '各数', '一个', '每个', '每个数','每个号', '号', '：', ':','个'}

SHENGXIAO_SET = set(ShengXiaoList)

# 定义基本单位和数字
CURRENCY_CHARS = {'元', '块', '圆', '米', 'A', 'a', '文', '园', '个数'}
CHINESE_NUMS = {'一', '二', '三', '四', '五', '六', '七', '八', '九', '十'}
CHINESE_UNITS = {'十', '百', '千', '万'}

def is_chinese_amount(text, pos):
    """检查从pos开始是否是中文金额"""
    if pos >= len(text):
        return False, 0
        
    # 检查第一个字符是否是中文数字
    if text[pos] not in CHINESE_NUMS and text[pos] not in CHINESE_UNITS:
        return False, 0
        
    end_pos = pos
    found_number = False
    
    while end_pos < len(text):
        char = text[end_pos]
        # 如果是中文数字或单位，继续检查
        if char in CHINESE_NUMS or char in CHINESE_UNITS:
            found_number = True
            end_pos += 1
        # 如果遇到其他字符���结束检查
        else:
            break
            
    if found_number:
        return True, end_pos - pos
    return False, 0

def is_range_expression(text):
    """检查是否是范围表达式，并返回起始和结束数字"""
    # 提取所有数字
    numbers = [int(n) for n in re.findall(r'\d+', text)]
    if len(numbers) != 2:
        return False, None, None
    
    # 检查是否包含范围关键词
    range_keywords = ['到', '至', '起至', '起到', '起']
    has_range_word = any(word in text for word in range_keywords)
    
    # 确保两个数字之间有范围关键词
    if has_range_word:
        first_num_pos = text.find(str(numbers[0]))
        second_num_pos = text.find(str(numbers[1]))
        text_between = text[first_num_pos + len(str(numbers[0])):second_num_pos]
        
        # 个数字之间是否包含范围关键词
        if any(word in text_between for word in range_keywords):
            return True, numbers[0], numbers[1]
    
    return False, None, None

# 在全局常量区域添加
HEAD_PATTERN = re.compile(r'([一二三四1234])[字头数|字头|头数|头]')
HEAD_NUMBERS = {
    '一': range(10, 20),
    '二': range(20, 30),
    '三': range(30, 40),
    '四': range(40, 50),
    '1': range(10, 20),
    '2': range(20, 30),
    '3': range(30, 40),
    '4': range(40, 50)
}

# 在全局常量区域添加（放在 HEAD_NUMBERS 后面）
TAIL_NUMBERS = {
    '0': [10, 20, 30, 40],
    '1': [11, 21, 31, 41],
    '2': [12, 22, 32, 42],
    '3': [13, 23, 33, 43],
    '4': [14, 24, 34, 44],
    '5': [15, 25, 35, 45],
    '6': [16, 26, 36, 46],
    '7': [17, 27, 37, 47],
    '8': [18, 28, 38, 48],
    '9': [19, 29, 39, 49],
    '一': [11, 21, 31, 41],
    '二': [12, 22, 32, 42],
    '三': [13, 23, 33, 43],
    '四': [14, 24, 34, 44],
    '五': [15, 25, 35, 45],
    '六': [16, 26, 36, 46],
    '七': [17, 27, 37, 47],
    '八': [18, 28, 38, 48],
    '九': [19, 29, 39, 49],
    '零': [10, 20, 30, 40]
}

# 添加大小单双映射
NUMBER_TYPES = { 
    '大单': [i for i in range(25, 50) if i % 2 == 1],
    '大双': [i for i in range(25, 50) if i % 2 == 0],
    '小单': [i for i in range(1, 25) if i % 2 == 1],
    '小双': [i for i in range(1, 25) if i % 2 == 0],
    '单': [i for i in range(1, 50) if i % 2 == 1],
    '双': [i for i in range(1, 50) if i % 2 == 0],
    '大': list(range(25, 50)),
    '小': list(range(1, 25))
}

# 定义基础赔率映射字典
PAYOUT_RATES = {
    '平特一肖': 2,
    '平特': 2,
    '2连肖': 4,
    '3��肖': 10,
    '4连肖': 30,
    '5连肖': 100,
    '特肖': 11
}

# 定��特殊生肖规则
SPECIAL_ZODIAC_RULES = {
    '平特': {
        '龙': 1.8,
        'default': 2
    },
    '2连肖': {
        '龙': 3.5,
        'default': 4
    },
    '3连肖': {
        '龙': 8,
        'default': 10
    },
    '4连肖': {
        '龙': 25,
        'default': 30
    },
    '5连肖': {
        '龙': 80,
        'default': 100
    },
    '特肖': {
        '龙': 9,
        'default': 11
    }
}

def parse_input(input_str):
    result = []  # 普通注额
    special_bets = {}  # 特殊注额
    
    temp_line = input_str
    
    # 1. 先处理连肖
    lianxiao_bets = parse_lianxiao(temp_line)
    special_bets.update(lianxiao_bets)
    
    # 移除已处理的连肖文本
    for pattern in [
        r'[二三四五2-5]连(?:肖)?复式[：:]?[鼠牛虎兔龙蛇马羊猴鸡狗猪]+各?(?:组)?(\d+)(?:[Aa元])?',
        r'[鼠牛虎兔龙蛇马羊猴鸡狗猪]+[二三四五2-5]连(?:肖)?复式各?(?:组)?(\d+)(?:[Aa元])?',
        r'复式[二三四五2-5]连(?:肖)?[：:]?[鼠牛虎兔龙蛇马羊猴鸡狗猪]+各?(?:组)?(\d+)(?:[Aa元])?',
        r'[二三四五2-5]连(?:肖)?[鼠牛虎兔龙蛇马羊猴鸡狗猪]+各?(?:组)?(\d+)(?:[Aa元])?',
        r'[鼠牛虎兔龙蛇马羊猴鸡狗猪]+[二三四五2-5]连(?:肖)?各?(?:组)?(\d+)(?:[Aa元])?'
    ]:
        temp_line = re.sub(pattern, '', temp_line)
    print(f"移除连肖后的文本: {temp_line}")
    
    # 2. 预先处理特肖和平特数据
    special_patterns = [
        (r'特肖([鼠牛虎兔龙蛇马羊猴鸡狗猪])各?(\d+|[一二三四五六七八九十百千万]+)元?', '特肖'),
        (r'([鼠牛虎兔龙蛇马羊猴鸡狗猪])特肖各?(\d+|[一二三四五六七八九十百千万]+)元?', '特肖'),
        (r'平特一肖([鼠牛虎兔龙蛇马羊猴鸡狗猪])各?(\d+|[一二三四五六七八九十百千万]+)元?', '平特'),
        (r'([鼠牛虎兔龙蛇马羊猴鸡狗猪])平特一肖各?(\d+|[一二三四五六七八九十百千万]+)元?', '平特'),
        (r'平特([鼠牛虎兔龙蛇马羊猴鸡狗猪])各?(\d+|[一二三四五六七八九十百千万]+)元?', '平特'),
        (r'([鼠牛虎兔龙蛇马羊猴鸡狗猪])平特各?(\d+|[一二三四五六七八九十百千万]+)元?', '平特')
    ]
    
    # 处理特肖和平特
    for pattern, type_name in special_patterns:
        matches = re.finditer(pattern, temp_line)
        for match in matches:
            zodiac = match.group(1)
            amount_str = match.group(2)
            amount = convert_amount(amount_str)
            
            if amount > 0 and zodiac in ShengXiaoBiao:
                numbers = ShengXiaoBiao[zodiac]
                combo_key = f"{type_name}_{zodiac}"
                special_bets[combo_key] = {
                    'numbers': numbers,
                    'amount': amount
                }
            
            # 从原文本中移除已处理的部分
            temp_line = temp_line.replace(match.group(0), '')
    print(f"移除特肖和平特后的文本: {temp_line}")
    # 处理平特多连复式
    multi_pattern = r'([二三四五六])?连复式[：:]((?:[鼠牛虎兔龙蛇马羊猴鸡狗猪],?)+)各?(\d+|[一二三四五六七八九十百千万]+)元?'
    matches = re.finditer(multi_pattern, temp_line)
    for match in matches:
        lian_count = convert_chinese_number(match.group(1)) if match.group(1) else 1
        zodiacs = re.findall(r'[鼠牛虎兔龙蛇马羊猴鸡狗猪]', match.group(2))
        amount_str = match.group(3)
        single_amount = convert_amount(amount_str)
        
        if single_amount > 0 and len(zodiacs) >= lian_count:
            zodiac_combinations = list(combinations(zodiacs, lian_count))
            for combo in zodiac_combinations:
                combo_numbers = set()
                for zodiac in combo:
                    if zodiac in ShengXiaoBiao:
                        combo_numbers.update(ShengXiaoBiao[zodiac])
                
                combo_key = f"{lian_count}连复式_{','.join(combo)}"
                special_bets[combo_key] = {
                    'numbers': sorted(list(combo_numbers)),
                    'amount': single_amount
                }
            # 从原文本中移除已处理的部分
            temp_line = temp_line.replace(match.group(0), '')
    print(f"移除连复式后的文本: {temp_line}")
    # 处理连肖（作为单独的处理部分）
    lianxiao_pattern = r'([鼠牛虎兔龙蛇马羊猴鸡狗猪]{2,5})([二三四五]?)连肖各?(\d+|[一二三四五六七八九十百千万]+)元?'
    matches = re.finditer(lianxiao_pattern, temp_line)
    for match in matches:
        shengxiao_str = match.group(1)
        lian_count = convert_chinese_number(match.group(2)) if match.group(2) else 2
        amount_str = match.group(3)
        amount = convert_amount(amount_str)
        
        if amount > 0 and len(shengxiao_str) >= lian_count:
            shengxiao_list = list(shengxiao_str)
            all_combinations = list(combinations(shengxiao_list, lian_count))
            
            for combo in all_combinations:
                combo_numbers = set()
                for zodiac in combo:
                    if zodiac in ShengXiaoBiao:
                        combo_numbers.update(ShengXiaoBiao[zodiac])
                
                combo_key = f"{lian_count}连肖_{''.join(combo)}"
                special_bets[combo_key] = {
                    'numbers': sorted(list(combo_numbers)),
                    'amount': amount
                }
        
        # 从原文本中移除已处理的部分
        temp_line = temp_line.replace(match.group(0), '')
    print(f"移除连肖后的文本: {temp_line}")

    # 替换所有表示"各"的词为统一的"各"
    temp_line = re.sub(r'每个数|每个号|各个|一个|每个|各号|号|特各|各数|名号|个数|个', '各', temp_line)
    # 统一货币单位为元
    temp_line = re.sub(r'米|文|快|块|A|a|园|员|圆|各数|斤','元',temp_line)
    # 替换所有分隔符为统一的"."
    temp_line = re.sub(r'[：\/｜|"‘"；~,:，。！\n  】【】【""]{\n !、·～`@#$%^&*()（）_——+=\\\-\]', '.', temp_line)
    # 移除期号和地区标识
    temp_line = re.sub(r'\d+期|[香港澳门六合彩特码包中奥噢]', '', temp_line)
    print(temp_line,'第四次处理')
    
    # 在处理字头数之前先处理尾数
    tail_matches = re.finditer(r'([0-9一二三四五六七八九零])尾各?(\d+|[一二三四五六七八九十百千万]+)元?', temp_line)
    for match in tail_matches:
        tail_key = match.group(1)
        amount_str = match.group(2)
        amount = convert_amount(amount_str)
        
        if amount > 0 and tail_key in TAIL_NUMBERS:
            for num in TAIL_NUMBERS[tail_key]:
                result.append(f"{num}:{amount}")
            # 从原文本中移除已处理的部分
            temp_line = temp_line.replace(match.group(0), '')
    print(f"移除尾数后的文本: {temp_line}")
    # 在分割行之前先处理字头数
    head_matches = re.finditer(r'([一二三四1234])(?:字头数|字头|头数|头)各?(\d+|[一二三四五六七八九十百千万]+)元?', temp_line)
    for match in head_matches:
        head_key = match.group(1)
        amount_str = match.group(2)
        amount = convert_amount(amount_str)
        
        if amount > 0 and head_key in HEAD_NUMBERS:
            for num in HEAD_NUMBERS[head_key]:
                result.append(f"{num}:{amount}")
            # 从原文本中移除已处理的部分
            temp_line = temp_line.replace(match.group(0), '')
    print(f"移除字头数后的文本: {temp_line}")
    # 添加分割处理逻辑
    current_pos = 0
    while current_pos < len(temp_line):
        # 检查货币单位（元）
        if temp_line[current_pos] in CURRENCY_CHARS:
            temp_line = temp_line[:current_pos+1] + '\n' + temp_line[current_pos+1:]
            current_pos += 2
            continue
            
        # 检查是否是中文金额
        is_amount, length = is_chinese_amount(temp_line, current_pos)
        if is_amount:
            # 检查后面是否有元
            next_pos = current_pos + length
            while next_pos < len(temp_line) and temp_line[next_pos].isspace():
                next_pos += 1
            # 如果后面没有"元"，则在中文金额后添加换行符
            if next_pos >= len(temp_line) or temp_line[next_pos] not in CURRENCY_CHARS:
                if current_pos + length < len(temp_line):
                    temp_line = temp_line[:current_pos+length] + '\n' + temp_line[current_pos+length:]
                    current_pos += length + 1
                else:
                    current_pos += length
                continue
        
        # 处理"各"的分割逻辑
        if temp_line[current_pos] == '各':
            # 向后查找分割点
            next_pos = current_pos + 1
            found_split = False
            
            while next_pos < len(temp_line):
                # 检查是否是货币字符
                if temp_line[next_pos] in CURRENCY_CHARS:
                    temp_line = temp_line[:next_pos+1] + '\n' + temp_line[next_pos+1:]
                    current_pos = next_pos + 2
                    found_split = True
                    break
                # 检查是否是小数点
                elif temp_line[next_pos] == '.':
                    # 确保小数点前后都是数字
                    if (next_pos > 0 and temp_line[next_pos-1].isdigit() and 
                        next_pos + 1 < len(temp_line) and temp_line[next_pos+1].isdigit()):
                        next_pos += 1
                        continue
                    temp_line = temp_line[:next_pos] + '\n' + temp_line[next_pos:]
                    current_pos = next_pos + 1
                    found_split = True
                    break
                # 检查是否是阿拉伯数字
                elif temp_line[next_pos].isdigit():
                    # 找到数字的结束位置
                    end_pos = next_pos
                    while end_pos < len(temp_line) and (temp_line[end_pos].isdigit() or temp_line[end_pos] == '.'):
                        end_pos += 1
                    if end_pos > next_pos:
                        temp_line = temp_line[:end_pos] + '\n' + temp_line[end_pos:]
                        current_pos = end_pos + 1
                        found_split = True
                        break
                # 检查是否是中文数字
                else:
                    is_amount, length = is_chinese_amount(temp_line, next_pos)
                    if is_amount and length > 0:
                        if next_pos + length < len(temp_line):
                            temp_line = temp_line[:next_pos+length] + '\n' + temp_line[next_pos+length:]
                            current_pos = next_pos + length + 1
                            found_split = True
                            break
                next_pos += 1
            
            if not found_split:
                current_pos += 1
            continue
        
        current_pos += 1
    print(f"移除各后的文本: {temp_line}")
    # 清理分割后的文本：去除每行首尾的空格和符号
    lines = temp_line.split('\n')
    cleaned_lines = []
    for line in lines:
        # 去除首尾空格和常见符号
        line = line.strip(' .\t\n\r')
        if line:  # 只保留非空行
            cleaned_lines.append(line)
    
    # 重新组合成文本
    cleaned_lines = '\n'.join(cleaned_lines)

    # 处理每一行
    for temp_line in cleaned_lines.split('\n'):
        temp_line = temp_line.strip()
        if not temp_line:  # 跳过空行
            continue
        
        processed = False  # 添加处理标志
        
        # 1. 新增：处理纯数字+元的格式（如"1235元"）
        if not processed:
            pure_number_pattern = re.compile(r'^(\d+)元?$')
            match = pure_number_pattern.match(temp_line)
            if match:
                num_str = match.group(1)
                if len(num_str) >= 3:  # 假设金额至少是三位数
                    # 分离号码和金额
                    num = int(num_str[:-2])  # 取前面的数字作为号码
                    amount = int(num_str[-2:])  # 取最后两位作为金额
                    if 1 <= num <= 49:
                        result.append(f"{num}:{amount}")
                        processed = True
                    
        # 2. 处理数字+中文数字混合格式（如"12三十五"或"12三十五元"）
        if not processed:
            mixed_pattern = re.compile(r'^(\d+)([一二三四五六七八九十百千万]+)元?$')
            match = mixed_pattern.match(temp_line)
            if match:
                num = int(match.group(1))
                amount = convert_amount(match.group(2))
                if 1 <= num <= 49 and amount > 0:
                    result.append(f"{num}:{amount}")
                    processed = True
                
        # 3. 理数字.中文数字格式（如"12.三十"或"12.三十元"）
        if not processed:
            dot_chinese_pattern = re.compile(r'^(\d+)\.([一二三四五六七八九十百千万]+)元?$')
            match = dot_chinese_pattern.match(temp_line)
            if match:
                num = int(match.group(1))
                amount = convert_amount(match.group(2))
                if 1 <= num <= 49 and amount > 0:
                    result.append(f"{num}:{amount}")
                    processed = True
                
        # 1. 处理大小单双格式（优先级最高）
        for type_name in NUMBER_TYPES:
            if type_name in temp_line:
                amount_match = re.search(r'各?([一二三四五六七八九十百千万\d]+)(?:元)?', temp_line)
                if amount_match:
                    amount = convert_amount(amount_match.group(1))
                    for num in NUMBER_TYPES[type_name]:
                        result.append(f"{num}:{amount}")
                    processed = True
                    break
        
        # 2. 处理范围表达式（如 "1到10各20元"）
        if not processed and ('到' in temp_line or '至' in temp_line):
            range_match = re.search(r'(\d+)[到至](\d+)各?([一二三四五六七八九十百千万\d]+)(?:元)?', temp_line)
            if range_match:
                start = int(range_match.group(1))
                end = int(range_match.group(2))
                amount = convert_amount(range_match.group(3))
                if 1 <= start <= 49 and 1 <= end <= 49:
                    for num in range(start, end + 1):
                        result.append(f"{num}:{amount}")
                    processed = True
        
        # 3. 处理带"各"的标准格式（包括生肖）
        if not processed and '各' in temp_line:
            parts = temp_line.split('各')
            if len(parts) >= 2:
                items_part = parts[0].strip()
                amount_part = parts[1].strip()
                amount = convert_amount(amount_part)
                
                if amount > 0:
                    for item in re.finditer(r'[鼠牛虎兔龙蛇马羊猴鸡狗猪]|\d+', items_part):
                        if item.group() in ShengXiaoBiao:
                            numbers = ShengXiaoBiao[item.group()]
                            for num in numbers:
                                result.append(f"{num}:{amount}")
                        else:
                            num = int(item.group())
                            if 1 <= num <= 49:
                                result.append(f"{num}:{amount}")
                    processed = True
        
        # 4. 处理点号分隔的数字+金额格式
        if not processed and '.' in temp_line:
            parts = [p.strip() for p in temp_line.split('.') if p.strip()]
            if len(parts) == 2 and parts[1].rstrip('元').isdigit():
                try:
                    num = int(parts[0])
                    amount = convert_amount(parts[1].rstrip('元'))
                    if 1 <= num <= 49 and amount > 0:
                        result.append(f"{num}:{amount}")
                        processed = True
                except ValueError:
                    pass
    
    return result, special_bets

#将中文数字转换阿拉伯数字
def convert_amount(amount_part):
    """将中文数字转换为阿拉伯数字"""
    # 如果是纯数字，直接返回
    if amount_part.isdigit():
        return int(amount_part)
    
    # 提取阿拉伯数字
    number_match = re.search(r'\d+', amount_part)
    if number_match:
        return int(number_match.group())
    
    # 清理输入，只保留中文数字
    cleaned_amount = ''.join(c for c in amount_part if c in CHINESE_DIGITS)
    if not cleaned_amount:
        return 0
        
    # 特殊处理"十"的情况
    if cleaned_amount == '十':
        return 10
    if cleaned_amount.startswith('十'):
        return 10 + (CHINESE_DIGITS[cleaned_amount[1]] if len(cleaned_amount) > 1 else 0)
    if cleaned_amount.endswith('十'):
        return CHINESE_DIGITS[cleaned_amount[0]] * 10
    
    # 处理普通中文数字
    result = 0
    temp = 0
    unit = 1
    
    for char in reversed(cleaned_amount):
        curr_value = CHINESE_DIGITS[char]
        if curr_value >= 10:
            unit = curr_value
            if temp == 0:
                temp = 1
        else:
            temp = curr_value
            result += temp * unit
            temp = 0
            unit = 1
    
    if temp:
        result += temp
        
    return result or 0

#从数据库中获取总金额
def get_total_amount_from_database():
    try:
        conn = sqlite3.connect('注额管理.db')
        cursor = conn.cursor()
        
        # 分别获取普通注额和特殊注额的总和
        cursor.execute('''
            SELECT 
                (SELECT COALESCE(SUM(额度), 0) FROM 注额管理 WHERE 特殊组合注额 IS NULL) +
                (SELECT COALESCE(SUM(特殊组合注额), 0) FROM 注额管理 WHERE 特殊组合注额 IS NOT NULL)
            AS total_amount
        ''')
        
        总注额 = cursor.fetchone()[0]
        conn.close()
        return 总注额 or 0
    except sqlite3.Error as e:
        print(f"获取总额错误: {e}")
        return 0

#拆分数大于 49
def split_number(num_str):
    result = []
    i = 0
    while i < len(num_str):
        if i + 1 < len(num_str) and int(num_str[i:i+2]) <= 49:
            result.append(num_str[i:i+2])
            i += 2
        else:
            result.append(num_str[i])
            i += 1
    return ','.join(result)

# Extract numbers and Chinese zodiac from input
def extract_numbers_and_shengxiao(input_text):
    # 直接提取生肖，修改正则表达式以匹配连续的生肖
    shengxiao_pattern = r'([鼠牛虎兔龙蛇马羊猴鸡狗猪]+)'
    shengxiao = []
    for match in re.finditer(shengxiao_pattern, input_text):
        shengxiao_str = match.group(1)
        # 将连续的生肖字符分开添加
        for char in shengxiao_str:
            if char in ShengXiaoList:
                shengxiao.append(char)
    
    # 提取并处数字
    numbers = []
    for num_str in re.findall(r'\d+', input_text):
        num = int(num_str.lstrip('0') or '0')
        if num > 49:
            # 对大于49的数字进行拆分
            numbers.extend(int(n) for n in split_number(str(num)).split(','))

        elif 1 <= num <= 49:
            numbers.append(num)
    
    # 过滤确保所有数字在1-49范围内
    valid_numbers = [num for num in numbers if 1 <= num <= 49]
    
    return valid_numbers, shengxiao

#输入框输出框颜色高
def highlight_numbers_and_shengxiao(input_numbers, input_shengxiao, parsed_keys, sorted_result):
    try:
        # 清除现有的高亮
        for text_widget in (text_input, output_text):
            for tag in text_widget.tag_names():
                if tag.startswith('highlight_'):
                    text_widget.tag_remove(tag, '1.0', 'end')
        
        # 如果输出为空，直接返回不进行高亮
        output_content = output_text.get("1.0", "end").strip()
        if not output_content:
            return
            
        input_content = text_input.get("1.0", "end")
        
        # 更深色的颜色列表
        colors = ['#FF0000', '#00AA00', '#0000FF', '#FF6600', '#9900CC',
                '#CC0066', '#006600', '#660066', '#0066CC', '#CC6600',
                '#006666', '#666600', '#660000', '#000066', '#CC3300']
        
        # 获取成功解析的数字和生肖
        parsed_numbers = set()
        parsed_zodiacs = set()
        
        # 从输出内容中提取所有���息
        special_bets = {}  # 存储特殊注额信息
        normal_bets = {}   # 存储普通注额信息
        
        for line in output_content.split('\n'):
            line = line.strip()
            if not line:
                continue
                
            # 处理普通注额
            if ':' in line and not any(special in line.lower() for special in ['平特', '连肖', '特肖']):
                try:
                    num, amount = line.split(':')
                    num = num.strip()
                    amount = amount.strip()
                    if amount not in normal_bets:
                        normal_bets[amount] = set()
                    normal_bets[amount].add(num)
                    parsed_numbers.add(num)
                    if len(num) == 1:
                        parsed_numbers.add(f"0{num}")
                except ValueError:
                    continue
            
            # 处理特殊注额
            elif any(special in line for special in ['平特_', '连肖_', '特肖_']):
                try:
                    bet_type = line.split('_')[0]
                    if bet_type not in special_bets:
                        special_bets[bet_type] = set()
                    # 提取生肖
                    for zodiac in ShengXiaoList:
                        if zodiac in line:
                            special_bets[bet_type].add(zodiac)
                            parsed_zodiacs.add(zodiac)
                except Exception:
                    continue
        
        # 分配颜色
        value_colors = {}
        color_index = 0
        
        # 1. 为普通注额分配颜色（按注额分组）
        for amount, numbers in normal_bets.items():
            color = colors[color_index % len(colors)]
            color_index += 1
            for num in numbers:
                value_colors[num] = color
                if len(num) == 1:
                    value_colors[f"0{num}"] = color
        
        # 2. 为特殊注额分配颜色（按类型分组）
        for bet_type, zodiacs in special_bets.items():
            color = colors[color_index % len(colors)]
            color_index += 1
            for zodiac in zodiacs:
                value_colors[zodiac] = color
                # 为生肖对应的数字也分配相同颜色
                if zodiac in ShengXiaoBiao:
                    for num in ShengXiaoBiao[zodiac]:
                        if str(num) in parsed_numbers:
                            value_colors[str(num)] = color
                            if num < 10:
                                value_colors[f"0{num}"] = color
        
        # 高亮处理
        def highlight_text(text_widget, content, is_input=True):
            # 处理数字
            number_pattern = r'(?:^|[^0-9])(\d+)(?=[^\d]|$)'
            for match in re.finditer(number_pattern, content):
                num = match.group(1)
                base_num = str(int(num))
                
                # 只高亮成功解析的数字
                if is_input and base_num not in parsed_numbers:
                    continue
                    
                if base_num in value_colors:
                    start_idx = f"1.0 + {match.start(1)} chars"
                    end_idx = f"1.0 + {match.end(1)} chars"
                    tag_name = f'highlight_group_{value_colors[base_num]}'
                    if tag_name not in text_widget.tag_names():
                        text_widget.tag_configure(tag_name, foreground=value_colors[base_num])
                    text_widget.tag_add(tag_name, start_idx, end_idx)
            
            # 处理生肖
            if is_input:  # 只在输入框处理生肖
                for zodiac in ShengXiaoList:
                    if zodiac in parsed_zodiacs and zodiac in value_colors:
                        start = 0
                        while True:
                            start = content.find(zodiac, start)
                            if start == -1:
                                break
                            tag_name = f'highlight_group_{value_colors[zodiac]}'
                            if tag_name not in text_widget.tag_names():
                                text_widget.tag_configure(tag_name, foreground=value_colors[zodiac])
                            text_widget.tag_add(tag_name, f"1.0 + {start} chars", f"1.0 + {start + len(zodiac)} chars")
                            start += len(zodiac)
        
        # 应用高亮
        highlight_text(text_input, input_content, True)
        highlight_text(output_text, output_content, False)
        
    except Exception as e:
        print(f"高亮处理出错: {str(e)}")
#将解析结果保存到数据库
def save_to_database(sorted_result, tag, special_bets=None):
    try:
        conn = sqlite3.connect('注额管理.db')
        cursor = conn.cursor()
        
        # 获取本地时区
        local_tz = pytz.timezone('Asia/Shanghai')
        # 获取当前本地时间
        current_time = datetime.now(local_tz).strftime('%Y-%m-%d %H:%M:%S.%f')
        
        # 获取原始输入数据和标识
        original_input = text_input.get("1.0", "end-1c")
        name = name_entry.get().strip()  # 从名字输入框获取标识
        
        # 保存普通注额
        for number, result in sorted_result.items():
            amount = result['注额']
            count = result['计数']
            if amount > 0:  # 只保存有注额的记录
                cursor.execute('''
                    INSERT INTO 注额管理 (
                        编号, 额度, 计数, 标签, 原始数据, 标识, 时间戳
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (number, amount, count, tag, original_input, name, current_time))
        
        # 保存特殊注额
        if special_bets:
            for combo_key, bet_info in special_bets.items():
                cursor.execute('''
                    INSERT INTO 注额管理 (
                        编号, 额度, 计数, 标签, 原始数据, 
                        特殊组合注额, 特殊组合类型, 标识, 时间戳
                    ) VALUES (
                        NULL, NULL, 1, ?, ?, ?, ?, ?, ?
                    )
                ''', (tag, original_input, bet_info['amount'], combo_key, name, current_time))

        conn.commit()
        
        # 打印保存信息
        print("\n=== 保存的注额信息 ===")
        total_normal = sum(result['注额'] for result in sorted_result.values())
        total_special = sum(bet_info['amount'] for bet_info in special_bets.values()) if special_bets else 0
        print(f"普通注额: {total_normal}元")
        print(f"特殊注额: {total_special}元")
        print(f"总注额: {total_normal + total_special}元")
        print(f"标识: {name if name else '未填写'}")
        
    except sqlite3.Error as e:
        print(f"数据库错误: {e}")
        # 显示具体的错误信息
        show_message(f"保存失败: {str(e)}")
    finally:
        conn.close()

#更新注额管理管理窗口中显示的数据
def update_data(tree):
    try:
        # 清空现有数据
        for i in tree.get_children():
            tree.delete(i)
            
        # 获取最大亏损值
        max_loss_text = max_loss_entry.get().strip()
        max_loss = float(max_loss_text) if max_loss_text else None
        print(f"最大亏损设置: {max_loss}")
        
        conn = sqlite3.connect('注额管理.db')
        cursor = conn.cursor()
        # 配置标签样式
        tree.tag_configure('negative_profit', foreground='red')  # 负盈利显示红色
        tree.tag_configure('need_reduction', foreground='red', background='yellow')  # 需��减注显示黄色和橙色背景
        tree.tag_configure('positive_profit', foreground='green')  # 正盈利显示绿色

        # 计算特殊注额总和
        cursor.execute('''
            SELECT COALESCE(SUM(特殊组合注额), 0) as special_total
            FROM 注额管理 
            WHERE 特殊组合注额 IS NOT NULL
            AND (
                (? = 0 AND 标签 = '澳门') OR 
                (? = 0 AND 标签 = '香港')
            )
        ''', (is_macau_hidden[0], is_hongkong_hidden[0]))
        special_total = cursor.fetchone()[0] or 0

        # 创建临时表存储所有可能号码
        cursor.execute('''
            CREATE TEMP TABLE IF NOT EXISTS all_numbers (
                编号 TEXT PRIMARY KEY
            )
        ''')
        cursor.execute('DELETE FROM all_numbers')
        
        # 插入1-49号码（确保两位数格式）
        for i in range(1, 50):
            number = f"{i:02d}"
            cursor.execute('INSERT INTO all_numbers (编号) VALUES (?)', (number,))
        
        print("临时表创建完成")
        
        # 获取数据
        query = '''
    WITH TotalSums AS (
        SELECT 
            SUM(CASE WHEN 标签 = '澳门' AND ? = 0 THEN COALESCE(额度, 0) ELSE 0 END) + 
            SUM(CASE WHEN 标签 = '澳门' AND ? = 0 THEN COALESCE(特殊组合注额, 0) ELSE 0 END) AS 澳门总额,
            SUM(CASE WHEN 标签 = '香港' AND ? = 0 THEN COALESCE(额度, 0) ELSE 0 END) + 
            SUM(CASE WHEN 标签 = '香港' AND ? = 0 THEN COALESCE(特殊组合注额, 0) ELSE 0 END) AS 香港总额,
            SUM(CASE WHEN 标签 = '澳门' AND ? = 0 THEN COALESCE(特殊组合注额, 0) ELSE 0 END) AS 澳门特殊额度,
            SUM(CASE WHEN 标签 = '香港' AND ? = 0 THEN COALESCE(特殊组合注额, 0) ELSE 0 END) AS 香港特殊额度
        FROM 注额管理
    ),
    NumberProfits AS (
        SELECT 
            an.编号,
            CAST(an.编号 AS INTEGER) as num_order,
            COALESCE(SUM(CASE WHEN n.标签 = '澳门' AND ? = 0 THEN n.额度 ELSE 0 END), 0) AS 澳门额度,
            COALESCE(SUM(CASE WHEN n.标签 = '澳门' AND ? = 0 THEN n.计数 ELSE 0 END), 0) AS 澳门计数,
            COALESCE(SUM(CASE WHEN n.标签 = '香港' AND ? = 0 THEN n.额度 ELSE 0 END), 0) AS 香港额度,
            COALESCE(SUM(CASE WHEN n.标签 = '香港' AND ? = 0 THEN n.计数 ELSE 0 END), 0) AS 香港计数,
            (SELECT 澳门总额 FROM TotalSums) AS 澳门总额,
            (SELECT 香港总额 FROM TotalSums) AS 香港总额,
            (SELECT 澳门特殊额度 FROM TotalSums) AS 澳门特殊额度,
            (SELECT 香港特殊额度 FROM TotalSums) AS 香港特殊额度,
            (SELECT 澳门总额 + 香港总额 FROM TotalSums) - 
            (COALESCE(SUM(CASE WHEN n.标签 = '澳门' AND ? = 0 THEN n.额度 ELSE 0 END), 0) + 
             COALESCE(SUM(CASE WHEN n.标签 = '香港' AND ? = 0 THEN n.额度 ELSE 0 END), 0)) * 47 AS profit
        FROM all_numbers an
        LEFT JOIN 注额管理 n ON an.编号 = n.编号 AND n.特殊组合注额 IS NULL
        GROUP BY an.编号
    )
    SELECT *
    FROM NumberProfits
    ORDER BY profit ASC
'''
        
        cursor.execute(query, (
            is_macau_hidden[0], is_macau_hidden[0],  # 澳门总额
            is_hongkong_hidden[0], is_hongkong_hidden[0],  # 香港总额
            is_macau_hidden[0], is_hongkong_hidden[0],  # 特殊额度
            is_macau_hidden[0], is_macau_hidden[0],  # 澳门额度和计数
            is_hongkong_hidden[0], is_hongkong_hidden[0],  # 香港额度和计数
            is_macau_hidden[0], is_hongkong_hidden[0]  # 盈利计算
        ))
        rows = cursor.fetchall()
        
        print(f"查询到 {len(rows)} 条记录")
        
        if rows:
            # 获取总额
            澳门总额 = rows[0][6] if rows[0][6] is not None else 0
            香港总额 = rows[0][7] if rows[0][7] is not None else 0
            displayed_total = 0
            if not is_macau_hidden[0]:
                displayed_total += 澳门总额
            if not is_hongkong_hidden[0]:
                displayed_total += 香港总额
            
            # 计算最大盈利（所有号码中最大的盈利值）
            max_total_profit = float('-inf')  # 初始化为负无穷
            # 计算正盈利号码数量
            positive_profit_count = 0
            for row in rows:
                _, number, macau_amount, macau_count, hk_amount, hk_count, *_ = row
                
                # 计算号码总注额
                total_amount = 0
                if not is_macau_hidden[0]:
                    total_amount += macau_amount
                if not is_hongkong_hidden[0]:
                    total_amount += hk_amount
                
                # 计算该号码的盈利
                profit = displayed_total - (total_amount * 47)
                if profit > 0:
                    positive_profit_count += 1
                max_total_profit = max(max_total_profit, profit)
            
            # 如果没有任何数据，将最大盈利设为0
            if max_total_profit == float('-inf'):
                max_total_profit = 0
            
            # 初始化计数器和调试信息
            original_positive_count = 0
            adjusted_positive_count = 0
            debug_info = []

            # 第一步：计算每个号码的原始盈利和建议减注
            adjustments = {}  # 存储每个号码的建议减注额
            total_reduction = 0  # 总减注额
            for row in rows:
                _, number, macau_amount, macau_count, hk_amount, hk_count, *_ = row
                
                # 计算号码总注额
                total_amount = 0
                if not is_macau_hidden[0]:
                    total_amount += macau_amount
                if not is_hongkong_hidden[0]:
                    total_amount += hk_amount
                
                # 计算原始盈利
                original_profit = displayed_total - (total_amount * 47)
                if original_profit > 0:
                    original_positive_count += 1
                
                # 如果超过最大亏损，计算建议减注
                if max_loss and original_profit < -max_loss:
                    needed_reduction = (abs(original_profit) - max_loss) / 47
                    adjustments[number] = needed_reduction
                    total_reduction += needed_reduction
            
            # 第二步：计算调整后的总额
            adjusted_total = displayed_total - total_reduction
            
            # 第三步：显示数据
            for row in rows:
                _, number, macau_amount, macau_count, hk_amount, hk_count, *_ = row
                
                total_amount = 0
                total_count = 0
                if not is_macau_hidden[0]:
                    total_amount += macau_amount
                    total_count += macau_count
                if not is_hongkong_hidden[0]:
                    total_amount += hk_amount
                    total_count += hk_count
                
                # 计算原始盈利
                original_profit = displayed_total - (total_amount * 47)
                

                if max_loss is not None:
                    if number in adjustments:
                        reduction = round(adjustments[number])
                        # 修改计算方式
                        adjusted_amount = total_amount - reduction  # 该号码调整后的注额
                        adjusted_profit = adjusted_total - (adjusted_amount * 47)  # 使用该号码的实际调整后注额计算
                        # print(f"号码 {number} 的详细计算过程:")
                        # print(f"总注额: {displayed_total}")
                        # print(f"总��注: {total_reduction}")
                        # print(f"调整后总额: {adjusted_total}")
                        # print(f"原注额: {total_amount}")
                        # print(f"建议减注: {reduction}")
                        # print(f"调整后注额: {adjusted_amount}")
                        # print(f"赔付金额: {adjusted_amount * 47}")
                        # print(f"最终盈利: {adjusted_profit}")
                        # print("-" * 50)
                        
                        amount_str = f"{total_amount}({total_count}注) 建议减注:{reduction:.0f}"
                        profit_str = f"{original_profit:.0f}→{adjusted_profit:.0f}"
                        tags = ('need_reduction',)
                        # 计算调整后的盈利情况
                        if adjusted_profit > 0:
                            adjusted_positive_count += 1
                    else:
                        adjusted_profit = adjusted_total - (total_amount * 47)
                        amount_str = f"{total_amount}({total_count}注)"
                        profit_str = f"{original_profit:.0f}→{adjusted_profit:.0f}"
                        tags = ('negative_profit',) if adjusted_profit < 0 else ('positive_profit',)
                        # 计算调整后的盈利情况
                        if adjusted_profit > 0:
                            adjusted_positive_count += 1
                else:
                    amount_str = f"{total_amount}({total_count}注)"
                    profit_str = f"{original_profit:.0f}"
                    tags = ('negative_profit',) if original_profit < 0 else ('positive_profit',)   
                
                # 插入数据到树形视图
                tree.insert("", "end", values=(
                    number,
                    amount_str,
                    profit_str,
                    f"{macau_amount}({macau_count}注)" if not is_macau_hidden[0] else "",
                    f"{hk_amount}({hk_count}注)" if not is_hongkong_hidden[0] else ""
                ), tags=tags)
            
            # 计算原始和调整后的赢率
            original_positive_count = sum(1 for row in rows if row[-1] > 0)  # 原始盈利号码数
            original_win_rate = original_positive_count / 49

            # 第二步:如果设置了最大亏损,计算调整后的赢率
            if max_loss is not None:
                total_reduction = sum(adjustments.values())
                adjusted_total = displayed_total - total_reduction
                debug_info.append(f"总减注额: {total_reduction}")
                debug_info.append(f"调整后总额: {adjusted_total}")
                
                # 重新计算每个号码调整后的盈亏情况
                adjusted_positive_count = 0
                processed_numbers = set()
                
                for row in rows:
                    number = row[1]
                    if number in processed_numbers:
                        continue
                    
                    processed_numbers.add(number)
                    total_amount = 0
                    if not is_macau_hidden[0]:
                        total_amount += row[2]  # macau_amount
                    if not is_hongkong_hidden[0]:
                        total_amount += row[4]  # hk_amount
                    
                    # 计算调整后的盈亏
                    adjusted_profit = 0
                    if number in adjustments:
                        # 对于需要减注的号码，减注后的盈亏应该是 -5644
                        debug_info.append(f"号码 {number}: 需要减注的号码，盈亏固定为 -5644")
                        adjusted_profit = -5644
                    else:
                        # 对于不需要减注的号码，使用调整后总额计算盈亏
                        adjusted_profit = adjusted_total - (total_amount * 47)
                        debug_info.append(f"号码 {number}: 原注额={total_amount}, 无需调整, 盈亏={adjusted_profit:.2f}")
                    
                    # 只有盈利的才计数
                    if adjusted_profit > 0:
                        adjusted_positive_count += 1
                        debug_info.append(f"号码 {number} 盈利")

                adjusted_win_rate = adjusted_positive_count / 49
                debug_info.append(f"\n最终统计:")
                debug_info.append(f"调整后盈利号码数: {adjusted_positive_count}")
                debug_info.append(f"调整后赢率: {adjusted_win_rate:.1%}")

                # 打印调试信息
                print("\n=== 赢率计算调试信息 ===")
                for info in debug_info:
                    print(info)
                print("=" * 30)

            # 更新表头显示
            if max_loss is not None:
                tree.heading("Total Amount", text=f"总注额: {displayed_total:.0f} | 建议总减注: {total_reduction:.0f}")
                tree.heading("Total Profit", text=f"调整后总额: {adjusted_total:.0f} | 赢率: {original_win_rate:.1%}→{adjusted_win_rate:.1%}")
            else:
                tree.heading("Total Amount", text=f"总注额: {displayed_total:.0f} | 平特类: {special_total:.0f}")
                tree.heading("Total Profit", text=f"最大盈利: {max_total_profit:.0f} | 赢率: {original_win_rate:.1%}")

        # 删除临时表
        cursor.execute('DROP TABLE IF EXISTS all_numbers')
        conn.close()

        # 在更新树形视图数据之后，添加更新标识总额的代码
        update_identifiers_display(tree)
        
    except Exception as e:
        messagebox.showerror("错误", f"更新数据时发生错误: {str(e)}")

def update_identifiers_display(tree):
    try:
        results = get_identifiers_and_totals()
        
        # 获取卡片容器
        cards_frame = tree.master.cards_frame
        canvas = tree.master.canvas
        
        # 清除现有卡片
        for widget in cards_frame.winfo_children():
            widget.destroy()
        
        # 创建新卡片
        for i, (identifier, total) in enumerate(results):
            if identifier.strip():
                # 创建卡片框架
                card = ttk.Frame(cards_frame, style="Card.TFrame")
                card.pack(side=tk.LEFT, padx=5, pady=5)
                
                # 添加标识名称
                title = ttk.Label(card, text=identifier, style="CardTitle.TLabel")
                title.pack(padx=10, pady=(5,0))
                
                # 添加金额
                amount = ttk.Label(card, 
                                text=f"{total:,.0f}",
                                style="CardAmount.TLabel")
                amount.pack(padx=10, pady=(0,5))
        
        # 更新Canvas的滚动区域
        cards_frame.update_idletasks()
        canvas.configure(scrollregion=canvas.bbox("all"))
        
    except Exception as e:
        print(f"更新标识显示时出错: {e}")

def get_identifiers_and_totals():
    try:
        conn = sqlite3.connect('注额管理.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT 
                标识,
                SUM(CASE 
                    WHEN 额度 IS NOT NULL THEN 额度 
                    WHEN 特殊组合注额 IS NOT NULL THEN 特殊组合注额 
                    ELSE 0 
                END) as total_amount
            FROM 注额管理
            WHERE 标识 IS NOT NULL AND 标识 != ''
            GROUP BY 标识
            HAVING total_amount > 0
            ORDER BY total_amount DESC
        ''')
        
        results = cursor.fetchall()
        conn.close()
        return results
    except sqlite3.Error as e:
        print(f"获取标识和总额时发生错误: {e}")
        return []
# 修改切换函数以新按钮文本
def toggle_macau_visibility(tree, button):
    is_macau_hidden[0] = not is_macau_hidden[0]
    button.config(text="澳门" if not is_macau_hidden[0] else "澳门")
    update_data(tree)

def toggle_hongkong_visibility(tree, button):
    is_hongkong_hidden[0] = not is_hongkong_hidden[0]
    button.config(text="香港" if not is_hongkong_hidden[0] else "香港")
    update_data(tree)

# 添加清所有数据的功能
def clear_all_data(tree):
    if messagebox.askyesno("确认", "确清空所有数据吗？"):
        try:
            conn = sqlite3.connect('注额管理.db')
            cursor = conn.cursor()
            cursor.execute('DELETE FROM 注额管理')
            conn.commit()
            conn.close()
            
            # 更新树形视图
            update_data(tree)
            
            # 重置计数器
            input_counter[0] = 0
            counter_label.config(text=f"已录入数据：{input_counter[0]}条")
            
            # 更新主界面总注额显示
            total_amount_label.config(text="总注额: 0")
            
            # 显示成功消息
            show_message("数据已清空", play_sound=True, sound_type="info")
            
        except sqlite3.Error as e:
            messagebox.showerror("数据库错误", f"清空数据失败: {e}")
            show_message("清空数据失败", play_sound=True, sound_type="error")

def open_management_window():
    management_window = tk.Toplevel(root)
    management_window.geometry("900x630")  
    management_window.title("注额管理")
    
    # 调整开奖号码输入框的布局，减少上下间距
    lottery_frame = ttk.Frame(management_window)
    lottery_frame.pack(side=tk.TOP, fill=tk.X, padx=5, pady=0)  # 减少pady
    ttk.Label(lottery_frame, text="开奖号码:").pack(side=tk.LEFT, padx=1)
    lottery_entry = ttk.Entry(lottery_frame, width=30)
    lottery_entry.pack(side=tk.LEFT, padx=1)
    
    # 减少标识显示区域的高度
    identifiers_frame = ttk.Frame(management_window)
    identifiers_frame.pack(side=tk.TOP, fill=tk.X, padx=5, pady=2)  # 减少pady
    
    # 减少Canvas的高度
    canvas = tk.Canvas(identifiers_frame, height=50)  # 从100改为60
    canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    
    # 添加滚动条
    scrollbar = ttk.Scrollbar(identifiers_frame, orient=tk.HORIZONTAL, command=canvas.xview)
    scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
    canvas.configure(xscrollcommand=scrollbar.set)
    
    # 创建卡片容器
    cards_frame = ttk.Frame(canvas)
    canvas.create_window((0, 0), window=cards_frame, anchor=tk.NW)
    
    # 将卡片容器保存为管理窗口的属性
    management_window.cards_frame = cards_frame
    management_window.canvas = canvas
    
    # 配置卡片容器的样式
    style = ttk.Style()
    style.configure("Card.TFrame", background="#f0f0f0", relief="raised", borderwidth=1)
    style.configure("CardTitle.TLabel", background="#f0f0f0", font=("Arial", 10, "bold"))
    style.configure("CardAmount.TLabel", background="#f0f0f0", font=("Arial", 12))
    
    def export_original_data():
        try:
            conn = sqlite3.connect('注额管理.db')
            cursor = conn.cursor()
            
            # 检查复选框状态
            selected_regions = []
            if macau_var.get():
                selected_regions.append("澳门")
            if hongkong_var.get():
                selected_regions.append("香港")
                
            if not selected_regions:
                show_message("请至少选择一个地区", play_sound=True, sound_type="warning")
                return
                
            # 获取开奖信息
            lottery_input = lottery_entry.get().strip()
            if not lottery_input:
                show_message("请输入开奖号码", play_sound=True, sound_type="warning")
                return
                
            numbers = [int(n) for n in re.split(r'[,，\s]+', lottery_input) if n.strip()]
            if len(numbers) not in [1, 7]:
                show_message("请输入1个特码或7个完整号码", play_sound=True, sound_type="warning")
                return
                
            # 处理号码
            if len(numbers) == 1:
                special_number = numbers[0]
                regular_numbers = []
            else:
                regular_numbers = numbers[:-1]
                special_number = numbers[-1]
            
            # 获取开奖生肖
            winning_zodiacs = set()
            for num in numbers:
                zodiac = get_zodiac_for_number(num)
                if zodiac:
                    winning_zodiacs.add(zodiac)
            
            # 使用与 update_data 相同的查询来获取总额
            cursor.execute('''
                WITH TotalSums AS (
                    SELECT 
                        SUM(CASE WHEN 标签 = '澳门' THEN COALESCE(额度, 0) ELSE 0 END) + 
                        SUM(CASE WHEN 标签 = '澳门' THEN COALESCE(特殊组合注额, 0) ELSE 0 END) AS 澳门总额,
                        SUM(CASE WHEN 标签 = '香港' THEN COALESCE(额度, 0) ELSE 0 END) + 
                        SUM(CASE WHEN 标签 = '香港' THEN COALESCE(特殊组合注额, 0) ELSE 0 END) AS 香港总额
                    FROM 注额管理
                )
                SELECT 澳门总额, 香港总额
                FROM TotalSums
            ''')
            
            totals = cursor.fetchone()
            region_totals = {
                "澳门": totals[0] if totals[0] is not None else 0,
                "香港": totals[1] if totals[1] is not None else 0
            }
            
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt")],
                initialfile=f"导出数据_{'-'.join(selected_regions)}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            )
            
            if not filename:
                return
                
            # 初始化总计统计
            grand_total_bets = 0
            grand_total_payouts = 0
            
            with open(filename, 'w', encoding='utf-8') as f:
                # 写入开奖信息
                if len(numbers) == 7:
                    f.write(f"=== 开奖号码: {','.join(map(str, regular_numbers))},特码{special_number} ===\n")
                    f.write(f"开奖生肖：{''.join(sorted(winning_zodiacs))}\n\n")
                else:
                    f.write(f"=== 特码: {special_number} ===\n")
                    f.write(f"特码生肖：{get_zodiac_for_number(special_number)}\n\n")
                
                # 分别处理每个选中的地区
                for region in selected_regions:
                    total_bets = region_totals[region]  # 使用查询获取的实际总注额
                    total_payouts = 0
                    
                    # 获取特码相关数据
                    query_special = """
                        SELECT 编号, 额度, 时间戳, 标签, 原始数据, 特殊组合类型, 特殊组合注额, 标识
                        FROM 注额管理
                        WHERE (编号 = ? OR 特殊组合类型 LIKE '%特码%' OR 特殊组合类型 LIKE '%特肖%')
                        AND 标签 = ?
                        ORDER BY 时间戳 ASC
                    """
                    cursor.execute(query_special, (str(special_number), region))
                    special_bets = cursor.fetchall()
                    
                    if special_bets:
                        # f.write(f"\n===== {region}特码 {special_number}({get_zodiac_for_number(special_number)}) 相关数据 =====\n")
                        for number, amount, timestamp, tag, original_data, special_type, special_amount, identifier in special_bets:
                            try:
                                bet_amount = float(special_amount if special_amount else amount or 0)
                                if bet_amount <= 0:
                                    continue
                                    
                                time_str = timestamp if isinstance(timestamp, str) else datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                                payout = bet_amount * 47
                                total_payouts += payout
                                f.write(f"\n----- {region}特码 {special_number}({get_zodiac_for_number(special_number)}) -----\n")
                                f.write(f"时间: {time_str}\n")
                                f.write(f"标签: {tag}\n")
                                f.write(f"来源: {identifier if identifier else ' '}\n")  # 增加标识输出
                                f.write(f"原始数据: {original_data}\n")
                                f.write(f"中奖金额: {bet_amount}\n")
                                if special_type:
                                    f.write(f"特殊类型: {special_type}\n")
                                f.write(f"赔付: {payout}\n")
                                f.write("-" * 50 + "\n")
                            except Exception as e:
                                print(f"处理特码记录时出错: {str(e)}, 跳过此记录")
                                continue
                    
                    # 只在输入7个号码时处理生肖组合
                    if len(numbers) == 7:
                        query_zodiac = """
                            SELECT 编号, 特殊组合注额, 时间戳, 标签, 原始数据, 特殊组合类型, 标识
                            FROM 注额管理
                            WHERE 特殊组合类型 IS NOT NULL 
                            AND 特殊组合类型 != ''
                            AND 特殊组合注额 > 0
                            AND 标签 = ?
                            ORDER BY 时间戳 ASC
                        """
                        cursor.execute(query_zodiac, (region,))
                        zodiac_bets = cursor.fetchall()
                        
                        if zodiac_bets:
                            # f.write(f"\n===== {region}生肖组合 {''.join(sorted(winning_zodiacs))} 中奖记录 =====\n")
                            for _, amount, timestamp, tag, original_data, special_type, identifier in zodiac_bets:
                                try:
                                    if not special_type or '_' not in special_type:
                                        continue
                                        
                                    bet_type, zodiacs_str = special_type.split('_', 1)
                                    bet_zodiacs = set(zodiacs_str)
                                    
                                    is_win, payout_rate, matching_zodiacs = check_zodiac_win(bet_type, bet_zodiacs, winning_zodiacs)
                                    
                                    if is_win:
                                        time_str = timestamp if isinstance(timestamp, str) else datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                                        bet_amount = float(amount)
                                        payout = bet_amount * payout_rate
                                        total_payouts += payout
                                        
                                        f.write(f"\n----- {region}开奖生肖 {''.join(sorted(winning_zodiacs))} -----\n")
                                        f.write(f"时间: {time_str}\n")
                                        f.write(f"标签: {tag}\n")
                                        f.write(f"来源: {identifier if identifier else ' '}\n")  # 增加标识输出
                                        f.write(f"投注类型: {special_type}\n")
                                        f.write(f"原始数据: {original_data}\n")
                                        f.write(f"中奖生肖: {''.join(sorted(matching_zodiacs))}\n")
                                        f.write(f"中奖金额: {bet_amount}\n")
                                        f.write(f"赔率: {payout_rate}\n")
                                        f.write(f"赔付: {payout}\n")
                                        f.write("-" * 50 + "\n")
                                        
                                except Exception as e:
                                    print(f"处理生肖组合记录时出错: {str(e)}, 记录: {special_type}")
                                    continue
                    
                    # 写入地区统计
                    f.write(f"\n===== {region}盈亏统计 =====\n")
                    f.write(f"总注额: {total_bets:.1f}\n")
                    f.write(f"总派彩: {total_payouts:.1f}\n")
                    f.write(f"盈利: {(total_bets - total_payouts):.1f}\n")
                    f.write("-" * 50 + "\n")
                    
            #         # 累加到总计
            #         grand_total_bets += total_bets
            #         grand_total_payouts += total_payouts
                
            #     # 写入总计统计
            #     f.write("\n===== 总计盈亏统计 =====\n")
            #     f.write(f"总注额: {grand_total_bets:.1f}\n")
            #     f.write(f"总派彩: {grand_total_payouts:.1f}\n")
            #     f.write(f"最终盈利: {(grand_total_bets - grand_total_payouts):.1f}\n")
            
            # show_message("数据导出成功", play_sound=True, sound_type="info")
            
        except Exception as e:
            print(f"导出数据时发生错误: {str(e)}")
            show_message(f"导出数据时发生错误: {str(e)}", play_sound=True, sound_type="error")
        finally:  # 这里需要有冒号
            if 'conn' in locals():
                conn.close()
    # 添加导出原始数据按钮
    export_original_button = ttk.Button(lottery_frame, text="导出原始数据", 
                                        command=export_original_data)
    export_original_button.pack(side=tk.LEFT, padx=5)

    tree = ttk.Treeview(management_window, columns=("Number", "Total Amount", "Total Profit", "Macau", "Hong Kong"), show='headings')
    tree.heading("Number", text="号码")
    tree.heading("Total Amount", text="总注额: ")
    tree.heading("Total Profit", text="盈利: ")
    tree.heading("Macau", text="澳门: ")
    tree.heading("Hong Kong", text="香港: ")

    tree.column("Number", width=50, anchor=tk.CENTER)
    tree.column("Total Amount", width=150, anchor=tk.CENTER)
    tree.column("Total Profit", width=234, anchor=tk.CENTER)
    tree.column("Macau", width=200, anchor=tk.CENTER)
    tree.column("Hong Kong", width=200, anchor=tk.CENTER)

    tree.tag_configure('negative_profit', foreground='red')
    tree.tag_configure('over_loss', foreground='red')

    def export_adjustments():
        if not max_loss_entry.get().strip():
            messagebox.showinfo("提示", "请先输入可接受亏损范围再计算调整")
            return
            
        try:
            # 获取所有需要调整的数据
            adjustment_data = []
            for item in tree.get_children():
                values = tree.item(item)['values']
                number = values[0]
                amount_str = values[1]
                
                # 检查是否包含建议减注
                if '建议减注' in amount_str:
                    reduction = float(amount_str.split('建议减注:')[1].strip(')'))
                    # 向上取整
                    rounded_reduction = math.ceil(reduction)
                    if rounded_reduction > 0:  # 只导出需要调整
                        adjustment_data.append(f"{number} 号：{rounded_reduction} 文")
            
            if not adjustment_data:
                messagebox.showinfo("提示", "没有需要调整的数据")
                return
                
            # 让用户选择保存位置
            file_path = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt")],
                initialfile="注额调整建议.txt"
            )
            
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("\n".join(adjustment_data))
                messagebox.showinfo("成功", "数已成功导出")
                
        except Exception as e:
            messagebox.showerror("错误", f"导出过程中发生错误: {str(e)}")

    button_frame = tk.Frame(management_window)
    button_frame.pack(side=tk.TOP, fill=tk.X, padx=5, pady=1)
    # 合并刷新和计算调整功能为一个钮


# 从右到左依次添加按钮和输入框
    refresh_button = tk.Button(button_frame, text="刷新/计算", command=lambda: update_data(tree))
    refresh_button.pack(side=tk.RIGHT, padx=1)
    # 添加亏损输入框到 button_frame
    global max_loss_entry
    max_loss_entry = ttk.Entry(button_frame, width=10)
    max_loss_entry.pack(side=tk.RIGHT, padx=1)
    
    ttk.Label(button_frame, text="盈利计算:").pack(side=tk.RIGHT, padx=1)

    # 创建复选框变量
    macau_var = tk.BooleanVar(value=not is_macau_hidden[0])
    hongkong_var = tk.BooleanVar(value=not is_hongkong_hidden[0])

    def on_macau_toggle():
        is_macau_hidden[0] = not macau_var.get()
        update_data(tree)

    def on_hongkong_toggle():
        is_hongkong_hidden[0] = not hongkong_var.get()
        update_data(tree)

    # 从右到左依次添加按钮和复选框
    hongkong_cb = tk.Checkbutton(button_frame, text="香港", variable=hongkong_var, command=on_hongkong_toggle)
    hongkong_cb.pack(side=tk.RIGHT, padx=1)
    
    macau_cb = tk.Checkbutton(button_frame, text="澳门", variable=macau_var, command=on_macau_toggle)
    macau_cb.pack(side=tk.RIGHT, padx=1)

    export_button = tk.Button(button_frame, text="导出调整建议", command=export_adjustments)
    export_button.pack(side=tk.RIGHT, padx=1)

    clear_all_button = tk.Button(button_frame, text="清空全部", command=lambda: clear_all_data(tree))
    clear_all_button.pack(side=tk.RIGHT, padx=1)

    update_data(tree)
    tree.pack(fill=tk.BOTH, expand=True, pady=10)


# 添加一个标志来指示是否正在处理剪贴板内容
is_processing = [False]
# 添加一个变量来存储输入框的最后内容
last_input_content = [""]

def check_clipboard():
    try:
        # 如果正在处理剪贴板内容，直接返回
        if is_processing[0]:
            return
            
        current_clipboard = pyperclip.paste()
        # 忽略空白内容
        if not current_clipboard.strip():
            return
            
        # 获取当前输入框内容
        current_input = text_input.get("1.0", "end-1c")
        
        # 果剪贴板内容与输入框内容相同，说明是从输入框复制的，直接忽略
        if current_clipboard == current_input:
            return
            
        # 如果内容与上次处理的相同，也忽略
        if current_clipboard == clipboard_content[0]:
            return
            
        clipboard_content[0] = current_clipboard
        if not is_first_check[0]:
            is_processing[0] = True  # 设置处理标志
            root.after(10, lambda: process_clipboard_content(current_clipboard))
        else:
            is_first_check[0] = False
            
    except Exception as e:
        print(f"剪贴板监控错误: {str(e)}")
    finally:
        root.after(500, check_clipboard)

def process_clipboard_content(content):
    try:
        text_input.delete("1.0", tk.END)
        text_input.insert("1.0", content)
        process_input(save_to_db=False)
        show_message("文本功,请仔细检查文本格式")
    except Exception as e:
        print(f"处理剪贴板内容错误: {str(e)}")
    finally:
        is_processing[0] = False  # 重置处理标志

#主界面
def init_main_window():
    # 声明局变量
    global status_var, text_input, output_text, total_amount_label, status_label, current_amount_label, number_count_label, name_entry
    
    # 创建状态量
    status_var = tk.StringVar()
    
    # 创建主框架
    main_frame = ttk.Frame(root, padding="10")
    main_frame.grid(row=0, column=0, sticky="nsew")
    
    # 配置主窗口网格权重
    root.grid_rowconfigure(0, weight=1)
    root.grid_columnconfigure(0, weight=1)
    
    # === 输入区域 ===
    input_frame = ttk.Frame(main_frame)
    input_frame.grid(row=0, column=0, sticky="ew")
    
    # 创建顶部标签行
    top_label_frame = ttk.Frame(input_frame)
    top_label_frame.grid(row=0, column=0, sticky="ew")
    
    # 配置列权重，使标识部分靠右
    top_label_frame.grid_columnconfigure(1, weight=1)  # 中间空白区域可伸缩
    
    # 录入计数标签（靠左）
    input_label = ttk.Label(top_label_frame, text=f"已录入数据：{input_counter[0]}条")
    input_label.grid(row=0, column=0, sticky="w")
    
    # 将标签保存为全局变量以便更新
    global counter_label
    counter_label = input_label
    
    # 创建标识部分（靠右）
    right_frame = ttk.Frame(top_label_frame)
    right_frame.grid(row=0, column=2, sticky="e")  # 放在第2列（最右边）
    
    # 标识标签和输入框
    name_label = ttk.Label(right_frame, text="标识")
    name_label.grid(row=0, column=0, padx=(0, 5))  # 只在标签右侧加入小间距
    
    name_entry = ttk.Entry(right_frame, width=10)
    name_entry.grid(row=0, column=1)
    
    text_input = tk.Text(input_frame, height=10, width=50)
    text_input.grid(row=1, column=0, sticky="ew")
    
    # === 按钮区域 ===
    button_frame = ttk.Frame(main_frame)
    button_frame.grid(row=1, column=0, sticky="ew", pady=5)
    
    # 配置列权重使按钮对齐
    button_frame.grid_columnconfigure(2, weight=1)  # 中间空白区域
    
    # 左侧按钮
    manage_button = ttk.Button(button_frame, text="打开注额管理", 
                            command=open_management_window)
    manage_button.grid(row=0, column=0, padx=5)
    
    # 重新解析按钮 - 放在右侧
    parse_button = ttk.Button(button_frame, text="重新解析", 
                            command=lambda: process_input(save_to_db=False))
    parse_button.grid(row=0, column=3, padx=18)
    
    # === 当前注额显示 ===
    current_amount_frame = ttk.Frame(main_frame)
    current_amount_frame.grid(row=2, column=0, sticky="ew", pady=5)
    
    # 配置列权重使标签居中
    current_amount_frame.grid_columnconfigure(0, weight=1)  # 左侧空白
    current_amount_frame.grid_columnconfigure(1, weight=0)  # 号码标签
    current_amount_frame.grid_columnconfigure(2, weight=0)  # 号码数值
    current_amount_frame.grid_columnconfigure(3, weight=0)  # 注额标签
    current_amount_frame.grid_columnconfigure(4, weight=0)  # 注额数值
    current_amount_frame.grid_columnconfigure(5, weight=1)  # 右侧空白
    
    # 号码数量显示
    ttk.Label(current_amount_frame, text="码数: ").grid(row=0, column=1)
    number_count_label = ttk.Label(
        current_amount_frame,
        text="0",
        font=("Arial", 12, "bold")
    )
    number_count_label.grid(row=0, column=2, padx=(0, 20))  # 添加右边距
    
    # 注显示
    ttk.Label(current_amount_frame, text="注额: ").grid(row=0, column=3)
    current_amount_label = ttk.Label(
        current_amount_frame,
        text="0",
        font=("Arial", 12, "bold")
    )
    current_amount_label.grid(row=0, column=4)
    
    # === 解析结果区域 ===
    result_frame = ttk.Frame(main_frame)
    result_frame.grid(row=3, column=0, sticky="w")
    
    result_label = ttk.Label(result_frame, text="解析结果：")
    result_label.grid(row=0, column=0, sticky="w")
    
    # 状态标签
    status_label = ttk.Label(result_frame, textvariable=status_var)
    status_label.grid(row=0, column=1, padx=10)
    
    # === 输出区域 ===
    output_frame = ttk.Frame(main_frame)
    output_frame.grid(row=4, column=0, sticky="nsew", pady=5)
    
    output_text = tk.Text(output_frame, height=20, width=40)
    output_text.grid(row=0, column=0, sticky="nsew")
    
    # === 底部区域 ===
    bottom_frame = ttk.Frame(main_frame)
    bottom_frame.grid(row=5, column=0, sticky="ew", pady=10)
    
    # 配置列权重使按钮对齐
    bottom_frame.grid_columnconfigure(2, weight=1)  # 中间空白区域
    
    # 总注额显示
    total_amount_label = ttk.Label(
        bottom_frame, 
        text="总注额: 0",
        font=("Arial", 18, "bold")
    )
    total_amount_label.grid(row=0, column=0, padx=5)
    
    # 解析并录入按钮 - 与上方重新解析按对齐
    save_button = ttk.Button(bottom_frame, text="解析并录入数据库", 
                            command=lambda: process_input(save_to_db=True))
    save_button.grid(row=0, column=3, padx=18)
    
    # 配置框架的网格权重
    main_frame.grid_rowconfigure(4, weight=1)
    main_frame.grid_columnconfigure(0, weight=1)
    output_frame.grid_rowconfigure(0, weight=1)
    output_frame.grid_columnconfigure(0, weight=1)
    
    # 初始化时更新总注额
    db_total_amount = get_total_amount_from_database()
    total_amount_label.config(text=f"总注额: {db_total_amount}")

# 添加一个通用的消息显示函数
def show_message(message, duration=3000, 持久显示=False, play_sound=False, sound_type="info"):
    status_var.set(message)
    if play_sound:
        play_system_sound(sound_type)
    if not 持久显示:  # 只有非持久性消息才会自动消失
        root.after(duration, lambda: status_var.set(""))

# 修改 process_input 函数中总注额显示部分
def process_input(save_to_db=True, event=None):
    global last_input_text, parsed_keys, last_saved_data, last_special_bets  # 添加 last_special_bets
    input_text = text_input.get("1.0", "end-1c")

    # 添加调试信息
    print("=== 开始处理输入 ===")
    print(f"保存到数据库: {save_to_db}")
    print(f"输入文本: {input_text}")
    print(f"上次保存的数据: {last_saved_data}")

    tag = "澳门"
    if "香港" in input_text or "香" in input_text or "港" in input_text:
        tag = "香港"

    formatted_output, special_bets = parse_input(input_text)
    
    # 添加调试信息
    print(f"解析结果 - 普通注额: {formatted_output}")
    print(f"解析结果 - 特殊注额: {special_bets}")

    output_text.delete("1.0", "end")
    
    # 先显示普通注额
    for item in formatted_output:
        output_text.insert("end", f"{item}\n")
    
    # 显示特殊注额
    for combo_key, bet_info in special_bets.items():
        output_text.insert("end", f"\n{combo_key}:\n")
        output_text.insert("end", f"号码: {bet_info['numbers']}\n")
        output_text.insert("end", f"金额: {bet_info['amount']}\n")

    result_dict = {}
    总注额 = 0
    total_numbers = 0  # 用于统计总计数��包括重复的）
    
    # 计普通注额
    for item in formatted_output:
        编号, 注额 = map(int, item.split(':'))
        if 编号 in result_dict:
            result_dict[编号]['注额'] += 注额
            result_dict[编号]['计数'] += 1
        else:
            result_dict[编号] = {'注额': 注额, '计数': 1}
        总注额 += 注额
        total_numbers += 1
    
    # 修改特殊注额计算方式
    特殊注额总和 = sum(bet_info['amount'] for bet_info in special_bets.values())
    总注额 += 特殊注额总和
    
    sorted_result = {编号: result_dict.get(编号, {'注额': 0, '计数': 0}) for 编号 in range(1, 50)}

    # 更新显示
    number_count_label.config(text=str(total_numbers))
    current_amount_label.config(text=str(总注额))  # 显示包含特殊注额的总额

    if save_to_db:
        # 比较普通注额和特殊注额
        is_same_data = (sorted_result == last_saved_data and 
                        special_bets == getattr(process_input, 'last_special_bets', {}))
        if is_same_data:
            show_message("当前数据与上次保存的数据相同，请勿重复保存", 持久显示=True)
            return
        
        save_to_database(sorted_result, tag, special_bets)
        # 更新计数器并显示
        input_counter[0] += 1
        counter_label.config(text=f"录入计数：{input_counter[0]}")
        
        # 深拷贝以确保数据立
        last_saved_data = {k: v.copy() for k, v in sorted_result.items()}
        process_input.last_special_bets = {k: v.copy() for k, v in special_bets.items()}
        
        print(f"更新后的last_saved_data: {last_saved_data}")
        print(f"更新后的last_special_bets: {process_input.last_special_bets}")
        
        db_total_amount = get_total_amount_from_database()
        total_amount_label.config(text=f"总注额: {db_total_amount}")
        show_message("-----结果已保存到数据库-----", play_sound=True, sound_type="info")
    else:
        show_message("解析成功")

    last_input_text = input_text
    input_numbers, input_shengxiao = extract_numbers_and_shengxiao(input_text)
    parsed_keys = input_numbers + input_shengxiao
    highlight_numbers_and_shengxiao(input_numbers, input_shengxiao, parsed_keys, sorted_result)

# 初始化静态变量
process_input.last_special_bets = {}

#添加生肖匹配的辅助函数
def get_zodiac_for_number(number):
    """获取一个号码对应的生肖"""
    for zodiac, nums in ShengXiaoBiao.items():
        if number in nums:
            return zodiac
    return None

def get_zodiacs_for_numbers(numbers):
    """获取一组号码对应的生肖集合"""
    zodiacs = set()
    for num in numbers:
        zodiac = get_zodiac_for_number(num)
        if zodiac:
            zodiacs.add(zodiac)
    return zodiacs

def check_zodiac_win(bet_type, bet_zodiacs, winning_zodiacs):
    """检查生肖是否中奖并计算赔率"""
    try:
        if '平特' in bet_type:
            # 对于平特，任意一个生肖命中即可
            matching_zodiacs = bet_zodiacs.intersection(winning_zodiacs)
            if matching_zodiacs:
                if '龙' in bet_zodiacs:  # 修改：检查投注生肖是否包含龙
                    payout_rate = SPECIAL_ZODIAC_RULES['平特']['龙']
                else:
                    payout_rate = PAYOUT_RATES['平特']
                return True, payout_rate, matching_zodiacs
        
        elif '特肖' in bet_type:
            # 特肖需要匹配特码生肖
            special_zodiac = get_zodiac_for_number(winning_number)
            if special_zodiac in bet_zodiacs:
                if '龙' in bet_zodiacs:  # 修改：检查投注生肖是否包含龙
                    payout_rate = SPECIAL_ZODIAC_RULES['特肖']['龙']
                else:
                    payout_rate = PAYOUT_RATES['特肖']
                return True, payout_rate, {special_zodiac}
        
        elif '连肖' in bet_type:
            # 对于连肖���需要所有生肖都命中
            if bet_zodiacs.issubset(winning_zodiacs):
                num_zodiacs = len(bet_zodiacs)
                bet_key = f'{num_zodiacs}连肖'
                
                # 修改：根据是否包含龙选择赔率
                if bet_key in SPECIAL_ZODIAC_RULES:
                    if '龙' in bet_zodiacs:
                        payout_rate = SPECIAL_ZODIAC_RULES[bet_key]['龙']
                    else:
                        payout_rate = SPECIAL_ZODIAC_RULES[bet_key]['default']
                    return True, payout_rate, bet_zodiacs
        
        return False, 0, set()
        
    except Exception as e:
        print(f"检查生肖中奖时出错: {str(e)}")
        return False, 0, set()

def parse_lianxiao(text):
    """处理连肖格式"""
    special_bets = {}
    
    print(f"开始处理连肖，输入文本: {text}")
    
    # 清理输入文本，按行分割
    lines = [line.strip() for line in text.split('\n')]
    
    for line in lines:
        print(f"处理行: {line}")
        
        # 预处理文本，移除特殊字符
        line = re.sub(r'[】【，,]', '', line)
        line = re.sub(r'[平]?([二三四五2-5])连肖?', r'\1连肖', line)
        line = re.sub(r'米|文|快|块|A|a|园|员|圆|各数|斤', '元', line)
        line = re.sub(r'[，,、一组]', '', line)
        print(f"预处理后的行: {line}")
        
        # 复式连肖模式
        复式_patterns = [
            r'([2-5二三四五])连(?:肖)?复式[：:]?([鼠牛虎兔龙蛇马羊猴鸡狗猪]+)各?(\d+)(?:[Aa元])?',
            r'([鼠牛虎兔龙蛇马羊猴鸡狗猪]+)([2-5二三四五])连(?:肖)?复式各?(\d+)(?:[Aa元])?',
            r'复式([2-5二三四五])连(?:肖)?[：:]?([鼠牛虎兔龙蛇马羊猴鸡狗猪]+)各?(\d+)(?:[Aa元])?',
            r'([鼠牛虎兔龙蛇马羊猴鸡狗猪]+)复式([2-5二三四五])连(?:肖)?各?(\d+)(?:[Aa元])?',
            r'([鼠牛虎兔龙蛇马羊猴鸡狗猪]+)([2-5二三四五])连复式(?:肖)?各?(\d+)(?:[Aa元])?',
            r'复式([2-5二三四五])连([鼠牛虎兔龙蛇马羊猴鸡狗猪]+)各?(\d+)(?:[Aa元])?',
            r'([2-5二三四五])连复式([鼠牛虎兔龙蛇马羊猴鸡狗猪]+)各?(\d+)(?:[Aa元])?'
        ]
        
        # 普通连肖模式
        普通连肖_patterns = [
            # 2连肖牛马100
            r'([2-5二三四五])连(?:肖)?([鼠牛虎兔龙蛇马羊猴鸡狗猪]{2,7})各?(\d+)(?:[Aa元])?',
            # 牛马2连肖100
            r'([鼠牛虎兔龙蛇马羊猴鸡狗猪]{2,7})([2-5二三四五])连(?:肖)?各?(\d+)(?:[Aa元])?',
            # 2连肖牛马各100
            r'([2-5二三四五])连肖([鼠牛虎兔龙蛇马羊猴鸡狗猪]{2,7})各?(\d+)(?:[Aa元])?',
            # 牛马2连肖各100
            r'([鼠牛虎兔龙蛇马羊猴鸡狗猪]{2,7})([2-5二三四五])连肖各?(\d+)(?:[Aa元])?'
        ]
        
        # 先尝试复式格式
        matched = False
        for pattern in 复式_patterns:
            match = re.match(pattern, line)
            if match:
                matched = True
                try:
                    # 提取连数和生肖
                    if match.group(1).isdigit() or match.group(1) in '二三四五':
                        lian_count = int(match.group(1)) if match.group(1).isdigit() else '二三四五'.index(match.group(1)) + 2
                        zodiacs = re.findall(r'[鼠牛虎兔龙蛇马羊猴鸡狗猪]', match.group(2))
                        amount = int(match.group(3))
                    else:
                        zodiacs = re.findall(r'[鼠牛虎兔龙蛇马羊猴鸡狗猪]', match.group(1))
                        lian_count = int(match.group(2)) if match.group(2).isdigit() else '二三四五'.index(match.group(2)) + 2
                        amount = int(match.group(3))
                    
                    print(f"复式解析结果 - 连数: {lian_count}, 生肖: {zodiacs}, 金额: {amount}")
                    
                    # 生成组合
                    if len(zodiacs) >= lian_count and 2 <= lian_count <= 5:
                        zodiac_combinations = list(combinations(zodiacs, lian_count))
                        for combo in zodiac_combinations:
                            combo_key = f"{lian_count}连肖_{','.join(combo)}"
                            special_bets[combo_key] = {
                                'type': f'{lian_count}连肖',
                                'numbers': get_zodiac_numbers(''.join(combo)),
                                'amount': amount
                            }
                            print(f"生成复式组合: {combo_key}")
                except Exception as e:
                    print(f"处理复式格式时出错: {str(e)}")
                break
        
        # 如果不是复式格式，尝试普通连肖格式
        if not matched:
            for pattern in 普通连肖_patterns:
                match = re.match(pattern, line)
                if match:
                    try:
                        # 提取连数和生肖
                        if match.group(1).isdigit() or match.group(1) in '二三四五':
                            lian_count = int(match.group(1)) if match.group(1).isdigit() else '二三四五'.index(match.group(1)) + 2
                            zodiacs = match.group(2)
                            amount = int(match.group(3))
                        else:
                            zodiacs = match.group(1)
                            lian_count = int(match.group(2)) if match.group(2).isdigit() else '二三四五'.index(match.group(2)) + 2
                            amount = int(match.group(3))
                        
                        print(f"普通连肖解析结果 - 连数: {lian_count}, 生肖: {zodiacs}, 金额: {amount}")
                        
                        # 生成组合
                        if len(zodiacs) >= lian_count and 2 <= lian_count <= 5:
                            combo_key = f"{lian_count}连肖_{zodiacs}"
                            special_bets[combo_key] = {
                                'type': f'{lian_count}连肖',
                                'numbers': get_zodiac_numbers(zodiacs),
                                'amount': amount
                            }
                            print(f"生成普通组合: {combo_key}")
                    except Exception as e:
                        print(f"处理普通连肖格式时出错: {str(e)}")
                    break
    
    print(f"连肖处理结果: {special_bets}")
    return special_bets

# 添加一个通用的播放声音函数
def play_system_sound(sound_type="info"):
    """
    播放系统提示音
    sound_type: 提示音类型，可以是 "info"、"warning"、"error"
    """
    try:
        if platform.system() == "Windows":
            import winsound
            sound_map = {
                "info": "SystemAsterisk",
                "warning": "SystemExclamation",
                "error": "SystemHand"
            }
            winsound.PlaySound(sound_map.get(sound_type, "SystemAsterisk"), 
                            winsound.SND_ALIAS | winsound.SND_ASYNC)
        elif platform.system() == "Darwin":  # macOS
            import os
            sound_map = {
                "info": "Tink",
                "warning": "Basso",
                "error": "Sosumi"
            }
            os.system(f"afplay /System/Library/Sounds/{sound_map.get(sound_type, 'Tink')}.aiff &")
    except Exception as e:
        print(f"播放系统提示音失败: {str(e)}")

def get_zodiac_numbers(zodiacs):
    """获取生肖对应的号码集合"""
    numbers = set()
    for zodiac in zodiacs:
        if zodiac in ShengXiaoBiao:
            numbers.update(ShengXiaoBiao[zodiac])
    return sorted(list(numbers))

#启动程序
if __name__ == '__main__':
    # 创建数库和表
    create_database_and_table()
    
    # 创建主窗口
    root = tk.Tk()
    root.title("数字和金额解析器")
    root.geometry("380x800")  # 设置初始窗口大小
    
    # 初始化主窗口UI
    init_main_window()

    # 初始化总注额显示
    db_total_amount = get_total_amount_from_database()
    total_amount_label.config(text=f"总注额: {db_total_amount}")

    
    # 初始化剪贴板监控
    clipboard_content = [""]
    check_clipboard()
    
    # 启动主循环
    root.mainloop()
