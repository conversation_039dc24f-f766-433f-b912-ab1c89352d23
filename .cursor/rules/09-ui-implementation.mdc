---
description: 
globs: 
alwaysApply: false
---
# UI实现指南

本文档详细说明如何将Android项目的Jetpack Compose UI组件转换为iOS的SwiftUI实现。两者都是现代声明式UI框架，概念上有很多相似之处，但实现细节有所不同。

## UI架构对比

| 功能 | Jetpack Compose | SwiftUI |
|-----|----------------|---------|
| 布局系统 | Column, Row, Box | VStack, HStack, ZStack |
| 状态管理 | State, MutableState | @State, @Binding |
| 生命周期 | LaunchedEffect, DisposableEffect | onAppear, onDisappear |
| 组件组合 | @Composable functions | View protocol |
| 列表 | LazyColumn, LazyRow | List, ScrollView+ForEach |
| 动画 | AnimatedVisibility, animateXXXAsState | withAnimation, .animation() |
| 主题 | MaterialTheme | .environment |

## 主要界面转换

### 1. 数据输入界面

Android ([DataInputScreen.kt](mdc:app/src/main/java/com/example/myshujuguanli/ui/screens/DataInputScreen.kt)):

```kotlin
@Composable
fun DataInputScreen(
    isTabletMode: Boolean = false,
    identifiers: List<String>,
    selectedIdentifier: String,
    onUpdateIdentifiers: (List<String>) -> Unit,
    // 其他参数...
) {
    var inputText by remember { mutableStateOf("") }
    var outputText by remember { mutableStateOf("") }
    
    Column(modifier = Modifier.fillMaxSize()) {
        // 头部区域
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(MaterialTheme.colorScheme.primaryContainer)
        ) {
            // 头部内容
        }
        
        // 输入区域
        TextField(
            value = inputText,
            onValueChange = { inputText = it },
            // 其他属性...
        )
        
        // 按钮区域
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            Button(onClick = { /* 清空 */ }) {
                Text("清空")
            }
            Button(onClick = { /* 解析 */ }) {
                Text("解析")
            }
        }
        
        // 输出区域
        Text(
            text = outputText,
            modifier = Modifier.padding(16.dp)
        )
    }
}
```

对应的SwiftUI实现:

```swift
struct DataInputScreen: View {
    @State private var inputText: String = ""
    @State private var outputText: String = ""
    @State private var identifiers: [String]
    @State private var selectedIdentifier: String
    
    let isTabletMode: Bool
    let onUpdateIdentifiers: ([String]) -> Void
    let onUpdateSelectedIdentifier: (String) -> Void
    // 其他属性...
    
    var body: some View {
        VStack {
            // 头部区域
            ZStack {
                Rectangle()
                    .fill(Color(.systemBackground))
                    .frame(maxWidth: .infinity)
                    .background(Color.accentColor.opacity(0.2))
                
                // 头部内容
            }
            
            // 输入区域
            TextEditor(text: $inputText)
                .frame(height: 150)
                .padding()
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                )
                .padding()
            
            // 按钮区域
            HStack(spacing: 20) {
                Button("清空") {
                    inputText = ""
                }
                .buttonStyle(.bordered)
                
                Button("解析") {
                    // 解析逻辑
                }
                .buttonStyle(.borderedProminent)
            }
            .padding()
            
            // 输出区域
            ScrollView {
                Text(outputText)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding()
            }
            .background(Color(.systemGray6))
            .cornerRadius(8)
            .padding()
            
            Spacer()
        }
    }
}
```

### 2. 管理界面

Android ([ManagementScreen.kt](mdc:app/src/main/java/com/example/myshujuguanli/ui/screens/ManagementScreen.kt)):

```kotlin
@Composable
fun ManagementScreen(
    isTabletMode: Boolean,
    mainActivity: MainActivity,
    onNavigateToDataManage: () -> Unit
) {
    var selectedTab by remember { mutableStateOf(0) }
    
    Column(modifier = Modifier.fillMaxSize()) {
        // 选项卡
        TabRow(selectedTabIndex = selectedTab) {
            Tab(
                selected = selectedTab == 0,
                onClick = { selectedTab = 0 },
                text = { Text("风险分析") }
            )
            Tab(
                selected = selectedTab == 1,
                onClick = { selectedTab = 1 },
                text = { Text("输赢计算") }
            )
        }
        
        // 内容区域
        when (selectedTab) {
            0 -> RiskAnalysisTab()
            1 -> WinLossCalculationTab()
        }
    }
}
```

对应的SwiftUI实现:

```swift
struct ManagementScreen: View {
    @State private var selectedTab = 0
    let isTabletMode: Bool
    let onNavigateToDataManage: () -> Void
    
    var body: some View {
        VStack {
            // 选项卡
            Picker("", selection: $selectedTab) {
                Text("风险分析").tag(0)
                Text("输赢计算").tag(1)
            }
            .pickerStyle(SegmentedPickerStyle())
            .padding()
            
            // 内容区域
            TabView(selection: $selectedTab) {
                RiskAnalysisTabView()
                    .tag(0)
                
                WinLossCalculationTabView()
                    .tag(1)
            }
            .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
        }
    }
}

struct RiskAnalysisTabView: View {
    var body: some View {
        // 风险分析UI
        Text("风险分析内容")
    }
}

struct WinLossCalculationTabView: View {
    var body: some View {
        // 输赢计算UI
        Text("输赢计算内容")
    }
}
```

## 公共组件转换

### 对话框

Android (Jetpack Compose):

```kotlin
@Composable
fun ActivationDialog(
    status: StatusInfo,
    onActivate: (String) -> Unit,
    onContinue: () -> Unit
) {
    var activationCode by remember { mutableStateOf("") }
    
    AlertDialog(
        onDismissRequest = { },
        title = { Text("激活状态") },
        text = {
            Column {
                Text("您的应用状态: ${status.message}")
                TextField(
                    value = activationCode,
                    onValueChange = { activationCode = it },
                    label = { Text("激活码") }
                )
            }
        },
        confirmButton = {
            TextButton(onClick = { onActivate(activationCode) }) {
                Text("激活")
            }
        },
        dismissButton = {
            TextButton(onClick = onContinue) {
                Text("继续使用")
            }
        }
    )
}
```

对应的SwiftUI实现:

```swift
struct ActivationDialog: View {
    @State private var activationCode: String = ""
    let status: StatusInfo
    let onActivate: (String) -> Void
    let onContinue: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            Text("激活状态")
                .font(.headline)
            
            Text("您的应用状态: \(status.message)")
                .multilineTextAlignment(.center)
            
            TextField("激活码", text: $activationCode)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .padding()
            
            HStack(spacing: 20) {
                Button("继续使用") {
                    onContinue()
                }
                .buttonStyle(.bordered)
                
                Button("激活") {
                    onActivate(activationCode)
                }
                .buttonStyle(.borderedProminent)
            }
        }
        .padding()
        .frame(width: 300)
    }
}
```

### 列表和卡片

Android (Jetpack Compose):

```kotlin
@Composable
fun IdentifierCard(
    identifier: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    onDelete: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp)
            .clickable(onClick = onClick),
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (isSelected) 8.dp else 2.dp
        ),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) 
                MaterialTheme.colorScheme.primaryContainer 
            else 
                MaterialTheme.colorScheme.surface
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(identifier)
            
            IconButton(onClick = onDelete) {
                Icon(Icons.Default.Delete, contentDescription = "删除")
            }
        }
    }
}
```

对应的SwiftUI实现:

```swift
struct IdentifierCard: View {
    let identifier: String
    let isSelected: Bool
    let onClick: () -> Void
    let onDelete: () -> Void
    
    var body: some View {
        Button(action: onClick) {
            HStack {
                Text(identifier)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Button(action: onDelete) {
                    Image(systemName: "trash")
                        .foregroundColor(.red)
                }
                .buttonStyle(.plain)
            }
            .padding()
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(isSelected ? Color.accentColor.opacity(0.2) : Color(.systemBackground))
            .cornerRadius(8)
            .shadow(color: Color.black.opacity(isSelected ? 0.2 : 0.1), 
                    radius: isSelected ? 5 : 2,
                    x: 0, y: isSelected ? 3 : 1)
        }
        .buttonStyle(.plain)
        .padding(.horizontal)
        .padding(.vertical, 4)
    }
}
```

## 状态管理对比

### Jetpack Compose状态管理:

```kotlin
// 本地状态
var inputText by remember { mutableStateOf("") }

// 屏幕间共享状态
class DataViewModel : ViewModel() {
    private val _data = MutableStateFlow<List<DataItem>>(emptyList())
    val data: StateFlow<List<DataItem>> = _data.asStateFlow()
    
    fun loadData() {
        // 加载数据
    }
}

@Composable
fun SomeScreen(viewModel: DataViewModel = viewModel()) {
    val data by viewModel.data.collectAsState()
    // 使用data
}
```

### SwiftUI状态管理:

```swift
// 本地状态
@State private var inputText: String = ""

// 屏幕间共享状态
class DataViewModel: ObservableObject {
    @Published var data: [DataItem] = []
    
    func loadData() {
        // 加载数据
    }
}

struct SomeScreen: View {
    @StateObject private var viewModel = DataViewModel()
    // 或者通过环境注入:
    // @EnvironmentObject var viewModel: DataViewModel
    
    var body: some View {
        // 使用viewModel.data
    }
}
```

## 主题和样式

### Jetpack Compose主题:

```kotlin
MaterialTheme(
    colorScheme = colorScheme,
    typography = Typography(),
    content = {
        // 应用内容
    }
)
```

### SwiftUI主题:

```swift
NavigationView {
    // 应用内容
}
.accentColor(Color.blue)
.preferredColorScheme(.light)
```

## 适配不同设备

### iPad适配:

```swift
struct ContentView: View {
    @Environment(\.horizontalSizeClass) var sizeClass
    
    var body: some View {
        if sizeClass == .regular {
            // iPad布局
            HStack {
                SidebarView()
                    .frame(width: 300)
                
                Divider()
                
                DetailView()
            }
        } else {
            // iPhone布局
            TabView {
                SidebarView()
                    .tabItem {
                        Label("列表", systemImage: "list.bullet")
                    }
                
                DetailView()
                    .tabItem {
                        Label("详情", systemImage: "info.circle")
                    }
            }
        }
    }
}
```

## 动画转换

### Jetpack Compose动画:

```kotlin
var expanded by remember { mutableStateOf(false) }
val animatedSize by animateFloatAsState(
    targetValue = if (expanded) 300f else 100f
)

Box(
    modifier = Modifier
        .size(animatedSize.dp)
        .clickable { expanded = !expanded }
)

// 可见性动画
AnimatedVisibility(
    visible = expanded,
    enter = expandVertically(),
    exit = shrinkVertically()
) {
    Text("更多内容")
}
```

### SwiftUI动画:

```swift
@State private var expanded = false
    
var body: some View {
    VStack {
        Rectangle()
            .fill(Color.blue)
            .frame(width: expanded ? 300 : 100, height: expanded ? 300 : 100)
            .animation(.spring(), value: expanded)
            .onTapGesture {
                expanded.toggle()
            }
        
        if expanded {
            Text("更多内容")
                .transition(.move(edge: .top).combined(with: .opacity))
        }
    }
    .animation(.easeInOut, value: expanded)
}
```

## 通用模式和最佳实践

1. **组件共享化**：创建通用组件，确保UI一致性
   
2. **预览功能**：充分利用SwiftUI的预览功能进行快速开发

3. **组合优于继承**：使用`ViewModifier`创建可重用样式

4. **按需懒加载**：使用`LazyVStack`和`LazyHStack`提高性能

5. **简化状态传递**：合理使用`@EnvironmentObject`减少prop drilling

6. **保持可访问性**：添加适当的标签和语义

```swift
Button(action: onDelete) {
    Image(systemName: "trash")
        .accessibilityLabel("删除")
}
```

## 性能优化

1. **避免过深的视图层次**：使用`Group`合并视图，减少嵌套

2. **减少不必要的状态更新**：谨慎使用`@State`和`@Published`

3. **缓存复杂计算**：对于复杂计算使用记忆化技术

4. **懒加载大型列表**：使用`List`和`LazyVStack`而不是`ForEach`+`ScrollView`

5. **图片优化**：使用适当的图片缓存机制
