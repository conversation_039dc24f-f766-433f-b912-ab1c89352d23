---
description: 
globs: 
alwaysApply: false
---
# 服务器端架构

## 主入口

[server.js](mdc:server.js) 是服务器的主入口文件，使用Express框架构建。

## 服务器功能

- **API接口**: 提供REST API接口，用于客户端通信
- **数据库操作**: 使用SQLite数据库存储和管理数据
- **安全验证**: 包含设备激活和状态验证
- **性能监控**: 使用Prometheus进行服务监控
- **缓存管理**: 使用node-cache进行数据缓存

## 关键组件

### 数据库连接与操作

服务器使用SQLite3作为数据库，主要表结构包括：
- devices表: 存储设备信息
- configs表: 存储配置信息
- request_logs表: 存储请求日志

### 中间件

- **安全中间件**: helmet用于HTTP安全头设置
- **速率限制**: express-rate-limit用于防止API滥用
- **请求日志**: morgan用于HTTP请求日志记录
- **CORS**: 跨域资源共享设置

### API路由

主要API端点包括：
- `/api/activate`: 设备激活
- `/api/status`: 设备状态检查
- `/api/data`: 数据相关操作

## 备用服务器

[server10086.js](mdc:server10086.js) 是备用服务器入口，在主服务器不可用时使用。
