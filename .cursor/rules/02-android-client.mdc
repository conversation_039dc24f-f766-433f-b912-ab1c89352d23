---
description: 
globs: 
alwaysApply: false
---
# Android客户端架构

## 主Activity

[MainActivity.kt](mdc:app/src/main/java/com/example/myshujuguanli/MainActivity.kt) 是应用的主入口点，负责初始化应用环境、处理导航和管理应用状态。

## 应用结构

### 界面组件(UI)

- **屏幕**: [app/src/main/java/com/example/myshujuguanli/ui/screens/](mdc:app/src/main/java/com/example/myshujuguanli/ui/screens)
  - 包含数据输入、管理、设置等主要功能界面
- **组件**: [app/src/main/java/com/example/myshujuguanli/ui/components/](mdc:app/src/main/java/com/example/myshujuguanli/ui/components)
  - 可复用的UI组件

### 视图模型(ViewModel)

[app/src/main/java/com/example/myshujuguanli/viewmodels/](mdc:app/src/main/java/com/example/myshujuguanli/viewmodels)

视图模型负责管理UI状态和业务逻辑，遵循MVVM架构模式。

### 数据模型(Model)

- **模型类**: [app/src/main/java/com/example/myshujuguanli/models/](mdc:app/src/main/java/com/example/myshujuguanli/models)
  - 数据实体类，表示应用中的主要数据结构
- **数据层**: [app/src/main/java/com/example/myshujuguanli/data/](mdc:app/src/main/java/com/example/myshujuguanli/data)
  - 数据访问层，包括本地数据库操作

### 服务(Service)

[app/src/main/java/com/example/myshujuguanli/services/](mdc:app/src/main/java/com/example/myshujuguanli/services)

包含后台服务和API调用相关代码。

### 工具类(Utils)

[app/src/main/java/com/example/myshujuguanli/utils/](mdc:app/src/main/java/com/example/myshujuguanli/utils)

包含各种辅助功能，如安全验证、设备识别、数据转换等。

## 主要功能

- 数据输入与管理
- 用户设置
- 安全验证
- 与服务器通信
