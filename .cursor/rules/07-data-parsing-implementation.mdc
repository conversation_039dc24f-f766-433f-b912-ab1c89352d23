---
description: 
globs: 
alwaysApply: false
---
# 数据解析实现指南

数据解析是本应用的核心功能，将Android版本的解析逻辑迁移到iOS需要特别注意。本文档详细说明解析引擎的工作原理和Swift实现方案。

## 核心解析引擎

Android版本的解析引擎主要在以下文件中实现：

- [DataParser.kt](mdc:app/src/main/java/com/example/myshujuguanli/utils/DataParser.kt) - 主要解析逻辑
- [ParseLearningEngine.kt](mdc:app/src/main/java/com/example/myshujuguanli/utils/ParseLearningEngine.kt) - 学习引擎

## 解析流程

1. **输入预处理**
   - 去除无关字符
   - 识别输入区域（香港/澳门）
   - 规范化输入格式

2. **模式识别**
   - 使用正则表达式识别数字和金额模式
   - 检测特殊投注类型
   - 处理多行输入和复合格式

3. **数据提取**
   - 从匹配模式中提取号码和金额
   - 计算汇总信息
   - 应用特殊规则处理

4. **结果生成**
   - 格式化输出结果
   - 生成统计数据
   - 准备数据库存储格式

## 关键正则表达式

以下是核心正则表达式模式的Swift转换示例：

```swift
// 基本号码和金额模式
let basicPattern = "\\b(\\d{1,2})\\D+(\\d+)\\b"

// 复杂模式 - 带注释的数字和金额
let complexPattern = "\\b(\\d{1,2})\\s*[号#]\\s*[：:=一]\\s*(\\d+)\\b"

// 特殊投注格式
let specialBetPattern = "([二三四五六七八九十](mdc:?:中[二三]|连[码肖]|不中|肖连?))\\D+(\\d+)\\b"
```

## Swift实现方案

将Android的解析逻辑转换为Swift时，建议采用以下结构：

```swift
class DataParser {
    // 配置项
    private let enableLearning: Bool
    private var patterns: [String: NSRegularExpression]
    
    // 学习引擎
    private let learningEngine: ParseLearningEngine
    
    init(enableLearning: Bool = true) {
        self.enableLearning = enableLearning
        self.patterns = [:]
        self.learningEngine = ParseLearningEngine()
        
        // 初始化正则表达式
        initializePatterns()
    }
    
    private func initializePatterns() {
        // 编译并存储所有正则表达式
        do {
            patterns["basic"] = try NSRegularExpression(pattern: "\\b(\\d{1,2})\\D+(\\d+)\\b", options: [])
            // 更多模式...
        } catch {
            print("正则表达式编译错误: \(error)")
        }
    }
    
    func parse(input: String) -> ParseResult {
        // 1. 预处理输入
        let preprocessed = preprocess(input: input)
        
        // 2. 检测区域
        let region = detectRegion(input: preprocessed)
        
        // 3. 应用解析策略
        var result = ParseResult()
        
        // 尝试所有可能的解析模式
        if let matches = findMatches(input: preprocessed, patternKey: "basic") {
            result.merge(parseBasicMatches(matches))
        }
        
        // 尝试特殊模式
        // ...
        
        // 4. 如果启用了学习，将结果交给学习引擎
        if enableLearning {
            learningEngine.learnFromInput(input: preprocessed, result: result)
        }
        
        return result
    }
    
    // 辅助方法
    private func preprocess(input: String) -> String {
        // 输入预处理逻辑
        // ...
        return input
    }
    
    private func detectRegion(input: String) -> Region {
        // 区域检测逻辑
        // ...
        return .none
    }
    
    private func findMatches(input: String, patternKey: String) -> [NSTextCheckingResult]? {
        guard let regex = patterns[patternKey] else { return nil }
        let range = NSRange(input.startIndex..<input.endIndex, in: input)
        return regex.matches(in: input, options: [], range: range)
    }
    
    private func parseBasicMatches(_ matches: [NSTextCheckingResult]) -> ParseResult {
        // 解析基本匹配
        // ...
        return ParseResult()
    }
}
```

## 学习引擎

[ParseLearningEngine.kt](mdc:app/src/main/java/com/example/myshujuguanli/utils/ParseLearningEngine.kt)实现了模式学习功能，使解析器能够适应新的输入格式。Swift版本可以这样实现：

```swift
class ParseLearningEngine {
    // 已知模式存储
    private var learnedPatterns: [String] = []
    
    // 模式置信度
    private var patternConfidence: [String: Int] = [:]
    
    // 永久存储
    private let userDefaults = UserDefaults.standard
    
    init() {
        // 从持久化存储加载已学习的模式
        loadLearnedPatterns()
    }
    
    func learnFromInput(input: String, result: ParseResult) {
        // 分析输入和解析结果之间的关系
        // 尝试提取新模式
        if let newPattern = extractPattern(from: input, result: result) {
            // 验证新模式
            if validatePattern(newPattern) {
                // 添加到学习库
                addPattern(newPattern)
            }
        }
    }
    
    private func extractPattern(from input: String, result: ParseResult) -> String? {
        // 模式提取逻辑
        // ...
        return nil
    }
    
    private func validatePattern(_ pattern: String) -> Bool {
        // 模式验证逻辑
        // ...
        return false
    }
    
    private func addPattern(_ pattern: String) {
        if !learnedPatterns.contains(pattern) {
            learnedPatterns.append(pattern)
            patternConfidence[pattern] = 1
            saveLearnedPatterns()
        } else {
            patternConfidence[pattern] = (patternConfidence[pattern] ?? 0) + 1
        }
    }
    
    private func loadLearnedPatterns() {
        if let saved = userDefaults.array(forKey: "learnedPatterns") as? [String] {
            learnedPatterns = saved
        }
        
        if let confidence = userDefaults.dictionary(forKey: "patternConfidence") as? [String: Int] {
            patternConfidence = confidence
        }
    }
    
    private func saveLearnedPatterns() {
        userDefaults.set(learnedPatterns, forKey: "learnedPatterns")
        userDefaults.set(patternConfidence, forKey: "patternConfidence")
    }
}
```

## 特殊情况处理

数据解析需要处理多种特殊情况：

1. **复合输入**：同时包含多种格式的输入
2. **模糊匹配**：不完全符合已知模式的输入
3. **错误处理**：处理格式不正确的输入
4. **生肖转换**：将生肖名称转换为对应数字

## 性能优化

iOS解析引擎应注意以下性能优化：

1. **预编译正则表达式**：避免重复编译
2. **异步处理**：将解析逻辑放在后台线程执行
3. **缓存结果**：对频繁使用的解析结果进行缓存
4. **渐进式解析**：处理大量输入时分段解析，避免UI卡顿

## 解析结果存储

解析结果需要转换为适合数据库存储的格式：

```swift
struct ParseResult {
    var numbers: [Int: Double] = [:] // 号码和金额
    var specialBets: [SpecialBet] = [] // 特殊投注
    var totalAmount: Double = 0
    var region: Region = .none
    
    mutating func merge(_ other: ParseResult) {
        // 合并结果逻辑
        for (number, amount) in other.numbers {
            numbers[number] = (numbers[number] ?? 0) + amount
        }
        
        specialBets.append(contentsOf: other.specialBets)
        totalAmount += other.totalAmount
        
        if region == .none {
            region = other.region
        } else if other.region != .none && region != other.region {
            region = .conflict
        }
    }
}
```

## 测试策略

为确保iOS版本与Android版本解析结果一致，应当：

1. 创建相同的测试用例集
2. 对比两个平台的解析结果
3. 确保特殊情况处理一致
4. 验证学习引擎的适应能力
