---
description: 
globs: 
alwaysApply: false
---
# 数据管理系统项目结构

本项目包含两个主要部分：
1. Android客户端应用（使用Kotlin和Jetpack Compose）


## 客户端(Android)

主入口：[app/src/main/java/com/example/myshujuguanli/MainActivity.kt](mdc:app/src/main/java/com/example/myshujuguanli/MainActivity.kt)

客户端使用MVVM架构：
- 模型(Model): [app/src/main/java/com/example/myshujuguanli/models/](mdc:app/src/main/java/com/example/myshujuguanli/models)
- 视图(View): [app/src/main/java/com/example/myshujuguanli/ui/](mdc:app/src/main/java/com/example/myshujuguanli/ui)
- 视图模型(ViewModel): [app/src/main/java/com/example/myshujuguanli/viewmodels/](mdc:app/src/main/java/com/example/myshujuguanli/viewmodels)

工具类：[app/src/main/java/com/example/myshujuguanli/utils/](mdc:app/src/main/java/com/example/myshujuguanli/utils)



## 构建配置

Android构建配置：
- [build.gradle.kts](mdc:build.gradle.kts)
- [app/build.gradle.kts](mdc:app/build.gradle.kts)

