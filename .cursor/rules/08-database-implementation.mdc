---
description: 
globs: 
alwaysApply: false
---
# 数据库实现指南

本文档详细说明如何将Android版本的SQLite数据库实现迁移到iOS平台，包括表结构、查询操作和数据迁移策略。

## 数据库架构

Android应用使用[DatabaseHelper.kt](mdc:app/src/main/java/com/example/myshujuguanli/utils/DatabaseHelper.kt)和[DatabaseUtils.kt](mdc:app/src/main/java/com/example/myshujuguanli/utils/DatabaseUtils.kt)来管理SQLite数据库。

主数据表结构如下：

```sql
CREATE TABLE IF NOT EXISTS 注额管理 (
    特码 INTEGER,
    注额 INTEGER,
    计数 INTEGER,
    地区 TEXT,
    标识 TEXT,
    原始数据 TEXT,
    特殊组合注额 INTEGER,
    特殊组合类型 TEXT,
    投注类型 TEXT DEFAULT 'normal',
    时间戳 TEXT,
    号码列表 TEXT
)
```

## iOS实现选项

在iOS上，有几种实现数据库的选择：

### 1. FMDB（推荐）

FMDB是一个Objective-C的SQLite封装库，可以在Swift中使用：

```swift
import FMDB

class DatabaseManager {
    static let shared = DatabaseManager()
    
    // 数据库文件路径
    private let dbPath: String
    private let queue: FMDatabaseQueue
    
    private init() {
        // 获取应用数据目录路径
        let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        dbPath = documentsDirectory.appendingPathComponent("注额管理.db").path
        
        // 使用数据库队列进行并发安全访问
        queue = FMDatabaseQueue(path: dbPath)
        
        // 初始化数据库结构
        createTableIfNeeded()
    }
    
    private func createTableIfNeeded() {
        queue.inDatabase { db in
            // 创建主表
            let createTableSQL = """
                CREATE TABLE IF NOT EXISTS 注额管理 (
                    特码 INTEGER,
                    注额 INTEGER,
                    计数 INTEGER,
                    地区 TEXT,
                    标识 TEXT,
                    原始数据 TEXT,
                    特殊组合注额 INTEGER,
                    特殊组合类型 TEXT,
                    投注类型 TEXT DEFAULT 'normal',
                    时间戳 TEXT,
                    号码列表 TEXT
                )
            """
            
            if !db.executeStatements(createTableSQL) {
                print("创建表失败: \(db.lastErrorMessage())")
            }
        }
    }
    
    // 插入数据方法
    func insertData(number: Int, amount: Int, tag: String, identifier: String, originalData: String) -> Bool {
        var success = false
        
        queue.inDatabase { db in
            let currentTimestamp = DateFormatter.localizedString(from: Date(), dateStyle: .medium, timeStyle: .medium)
            
            // 使用参数绑定防止SQL注入
            let insertSQL = """
                INSERT INTO 注额管理 (特码, 注额, 计数, 地区, 标识, 原始数据, 时间戳)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            
            success = db.executeUpdate(insertSQL, 
                                      withArgumentsIn: [number, amount, 1, tag, identifier, originalData, currentTimestamp])
            
            if !success {
                print("插入数据失败: \(db.lastErrorMessage())")
            }
        }
        
        return success
    }
    
    // 查询方法示例
    func getTotalAmount() -> Int {
        var total = 0
        
        queue.inDatabase { db in
            let query = """
                SELECT SUM(CASE
                    WHEN 投注类型 IS NULL OR 投注类型 = 'normal' THEN 注额
                    ELSE 特殊组合注额
                END) AS total
                FROM 注额管理
            """
            
            if let result = db.executeQuery(query, withArgumentsIn: []) {
                if result.next() {
                    total = Int(result.int(forColumn: "total"))
                }
                result.close()
            }
        }
        
        return total
    }
    
    // 其他数据库方法...
}
```

### 2. SQLite.swift

SQLite.swift是纯Swift实现的SQLite封装库：

```swift
import SQLite

class DatabaseManager {
    static let shared = DatabaseManager()
    
    // 定义表和列
    private let dataTable = Table("注额管理")
    private let colNumber = Expression<Int>("特码")
    private let colAmount = Expression<Int>("注额")
    private let colCount = Expression<Int>("计数")
    private let colRegion = Expression<String>("地区")
    private let colIdentifier = Expression<String>("标识")
    private let colOriginalData = Expression<String>("原始数据")
    private let colSpecialAmount = Expression<Int?>("特殊组合注额")
    private let colSpecialType = Expression<String?>("特殊组合类型")
    private let colBetType = Expression<String>("投注类型")
    private let colTimestamp = Expression<String>("时间戳")
    private let colNumberList = Expression<String?>("号码列表")
    
    private let db: Connection
    
    private init() {
        // 获取应用数据目录路径
        let path = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first!
        let dbPath = "\(path)/注额管理.db"
        
        // 连接数据库
        do {
            db = try Connection(dbPath)
            createTableIfNeeded()
        } catch {
            fatalError("无法连接数据库: \(error)")
        }
    }
    
    private func createTableIfNeeded() {
        do {
            try db.run(dataTable.create(ifNotExists: true) { table in
                table.column(colNumber)
                table.column(colAmount)
                table.column(colCount)
                table.column(colRegion)
                table.column(colIdentifier)
                table.column(colOriginalData)
                table.column(colSpecialAmount)
                table.column(colSpecialType)
                table.column(colBetType, defaultValue: "normal")
                table.column(colTimestamp)
                table.column(colNumberList)
            })
        } catch {
            print("创建表失败: \(error)")
        }
    }
    
    // 插入数据方法
    func insertData(number: Int, amount: Int, tag: String, identifier: String, originalData: String) -> Bool {
        do {
            let dateFormatter = DateFormatter()
            dateFormatter.dateStyle = .medium
            dateFormatter.timeStyle = .medium
            dateFormatter.locale = Locale(identifier: "zh_CN")
            dateFormatter.timeZone = TimeZone(identifier: "Asia/Shanghai")
            let timestamp = dateFormatter.string(from: Date())
            
            let insert = dataTable.insert(
                colNumber <- number,
                colAmount <- amount,
                colCount <- 1,
                colRegion <- tag,
                colIdentifier <- identifier,
                colOriginalData <- originalData,
                colTimestamp <- timestamp
            )
            
            try db.run(insert)
            return true
        } catch {
            print("插入数据失败: \(error)")
            return false
        }
    }
    
    // 查询方法示例
    func getTotalAmount() -> Int {
        do {
            // 对于normal类型，使用注额；对于特殊投注，使用特殊组合注额
            let query = "SELECT SUM(CASE WHEN 投注类型 = 'normal' OR 投注类型 IS NULL THEN 注额 ELSE 特殊组合注额 END) AS total FROM 注额管理"
            let result = try db.scalar(query) as? Int
            return result ?? 0
        } catch {
            print("查询失败: \(error)")
            return 0
        }
    }
    
    // 其他数据库方法...
}
```

### 3. Core Data

对于更复杂的数据关系，也可以考虑使用Core Data：

```swift
// AppDelegate.swift 或 SceneDelegate.swift 中初始化Core Data栈

lazy var persistentContainer: NSPersistentContainer = {
    let container = NSPersistentContainer(name: "BettingDataModel")
    container.loadPersistentStores { description, error in
        if let error = error {
            fatalError("无法加载Core Data存储: \(error)")
        }
    }
    return container
}()

// 创建BettingData.swift实体模型
import CoreData

@objc(BettingData)
public class BettingData: NSManagedObject {
    @NSManaged public var number: Int16
    @NSManaged public var amount: Int32
    @NSManaged public var count: Int16
    @NSManaged public var region: String
    @NSManaged public var identifier: String
    @NSManaged public var originalData: String
    @NSManaged public var specialAmount: Int32
    @NSManaged public var specialType: String?
    @NSManaged public var betType: String
    @NSManaged public var timestamp: String
    @NSManaged public var numberList: String?
}
```

## 数据库操作迁移

Android版本中的常见数据库操作及其iOS对应实现：

### 1. 插入数据

```swift
// FMDB实现示例
func insertData(number: Int, amount: Int, tag: String, identifier: String, originalData: String) -> Bool {
    var success = false
    queue.inDatabase { db in
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .medium, timeStyle: .medium)
        
        let insertSQL = """
            INSERT INTO 注额管理 (特码, 注额, 计数, 地区, 标识, 原始数据, 时间戳)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """
        
        success = db.executeUpdate(insertSQL, withArgumentsIn: [number, amount, 1, tag, identifier, originalData, timestamp])
    }
    return success
}
```

### 2. 批量插入

```swift
// FMDB实现示例
func insertBatchData(dataList: [(number: Int, amount: Int, tag: String, identifier: String, originalData: String)]) -> Bool {
    var success = true
    
    queue.inTransaction { db, rollback in
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .medium, timeStyle: .medium)
        
        for data in dataList {
            let insertSQL = """
                INSERT INTO 注额管理 (特码, 注额, 计数, 地区, 标识, 原始数据, 时间戳)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            
            let result = db.executeUpdate(insertSQL, withArgumentsIn: [
                data.number, data.amount, 1, data.tag, data.identifier, data.originalData, timestamp
            ])
            
            if !result {
                rollback.pointee = true
                success = false
                break
            }
        }
    }
    
    return success
}
```

### 3. 查询汇总数据

```swift
// FMDB实现示例
func getStats() -> (totalAmount: Int, macauAmount: Int, hongKongAmount: Int) {
    var totalAmount = 0
    var macauAmount = 0
    var hongKongAmount = 0
    
    queue.inDatabase { db in
        // 查询总金额
        if let result = db.executeQuery("SELECT SUM(注额) as total FROM 注额管理", withArgumentsIn: []) {
            if result.next() {
                totalAmount = Int(result.int(forColumn: "total"))
            }
            result.close()
        }
        
        // 查询澳门金额
        if let result = db.executeQuery("SELECT SUM(注额) as total FROM 注额管理 WHERE 地区 = '澳门'", withArgumentsIn: []) {
            if result.next() {
                macauAmount = Int(result.int(forColumn: "total"))
            }
            result.close()
        }
        
        // 查询香港金额
        if let result = db.executeQuery("SELECT SUM(注额) as total FROM 注额管理 WHERE 地区 = '香港'", withArgumentsIn: []) {
            if result.next() {
                hongKongAmount = Int(result.int(forColumn: "total"))
            }
            result.close()
        }
    }
    
    return (totalAmount, macauAmount, hongKongAmount)
}
```

## 数据迁移策略

从Android迁移数据到iOS有几种方案：

### 1. 直接复制数据库文件

如果两个平台的表结构完全相同，可以直接复制SQLite文件：

```swift
func importDatabaseFromURL(_ url: URL) -> Bool {
    let fileManager = FileManager.default
    let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
    let destination = documentsDirectory.appendingPathComponent("注额管理.db")
    
    // 如果目标文件已存在，先删除
    if fileManager.fileExists(atPath: destination.path) {
        do {
            try fileManager.removeItem(at: destination)
        } catch {
            print("无法删除现有数据库: \(error)")
            return false
        }
    }
    
    // 复制文件
    do {
        try fileManager.copyItem(at: url, to: destination)
        return true
    } catch {
        print("导入数据库失败: \(error)")
        return false
    }
}
```

### 2. JSON导出/导入

通过JSON格式进行数据交换：

```swift
// 导入JSON数据
func importFromJSON(_ jsonData: Data) -> Bool {
    do {
        guard let jsonArray = try JSONSerialization.jsonObject(with: jsonData) as? [[String: Any]] else {
            return false
        }
        
        return queue.inTransaction { db, rollback in
            for item in jsonArray {
                let number = item["特码"] as? Int ?? 0
                let amount = item["注额"] as? Int ?? 0
                let count = item["计数"] as? Int ?? 1
                let region = item["地区"] as? String ?? ""
                let identifier = item["标识"] as? String ?? ""
                let originalData = item["原始数据"] as? String ?? ""
                let timestamp = item["时间戳"] as? String ?? 
                    DateFormatter.localizedString(from: Date(), dateStyle: .medium, timeStyle: .medium)
                
                let insertSQL = """
                    INSERT INTO 注额管理 (特码, 注额, 计数, 地区, 标识, 原始数据, 时间戳)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """
                
                if !db.executeUpdate(insertSQL, withArgumentsIn: [
                    number, amount, count, region, identifier, originalData, timestamp
                ]) {
                    rollback.pointee = true
                    return false
                }
            }
            
            return true
        }
    } catch {
        print("解析JSON失败: \(error)")
        return false
    }
}
```

## 性能优化

在iOS上实现数据库操作时，应注意以下性能优化：

1. **使用事务**：批量操作时使用事务，减少磁盘I/O
2. **索引优化**：根据查询模式添加适当的索引
3. **参数绑定**：使用参数绑定而不是字符串拼接，提高安全性和性能
4. **异步操作**：将数据库操作放在后台线程

```swift
// 示例：在后台线程执行数据库操作
DispatchQueue.global(qos: .background).async {
    let result = DatabaseManager.shared.insertData(number: 1, amount: 100, tag: "澳门", identifier: "user1", originalData: "原始数据")
    
    // 在主线程更新UI
    DispatchQueue.main.async {
        if result {
            // 操作成功，更新UI
        } else {
            // 操作失败，显示错误
        }
    }
}
```

## 测试策略

为确保数据库迁移和操作正确：

1. **单元测试**：测试每个数据库操作函数
2. **集成测试**：测试完整的数据流
3. **迁移测试**：确保Android数据可以正确导入iOS版本
4. **性能测试**：测试大数据量下的性能表现
