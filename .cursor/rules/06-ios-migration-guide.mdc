---
description: 
globs: 
alwaysApply: false
---
# iOS 迁移指南

本文档提供从Android项目转换为iOS应用的详细指南，包括架构、数据模型、UI组件和功能实现方面的建议。

## 项目架构

Android项目使用MVVM架构，iOS项目应当保持相同的架构模式:

- **模型(Model)**: 数据模型类应从[DataModels.kt](mdc:app/src/main/java/com/example/myshujuguanli/utils/DataModels.kt)迁移到Swift结构体
- **视图(View)**: 从Jetpack Compose转换为SwiftUI
- **视图模型(ViewModel)**: 使用Swift的ObservableObject或Combine框架实现

## 核心数据模型

关键数据模型定义在[DataModels.kt](mdc:app/src/main/java/com/example/myshujuguanli/utils/DataModels.kt)中，需转换为Swift结构体：

```swift
// 示例Swift数据模型
struct NumberData: Codable {
    var totalAmount: Int = 0
    var profit: Int = 0
    var macauAmount: Int = 0
    var hongKongAmount: Int = 0
    var count: Int = 0
    var specialBets: [String: Int] = [:]
}

struct Stats: Codable {
    var totalAmount: Int = 0
    var maxProfit: Int = 0
    var winRate: Float = 0.0
    var macauAmount: Int = 0
    var hongKongAmount: Int = 0
}

// 更多数据模型...
```

## 数据库实现

Android项目使用SQLite，iOS也应使用SQLite或CoreData:

- [DatabaseUtils.kt](mdc:app/src/main/java/com/example/myshujuguanli/utils/DatabaseUtils.kt)内的数据库操作需转换为Swift实现
- 可使用FMDB或SQLite.swift库简化SQLite操作
- 或使用CoreData进行面向对象的持久化存储

## 用户界面组件

主要界面包括:

1. **数据输入界面**([DataInputScreen.kt](mdc:app/src/main/java/com/example/myshujuguanli/ui/screens/DataInputScreen.kt))
   - 输入文本解析
   - 标识符管理
   - 区域(香港/澳门)切换

2. **管理界面**([ManagementScreen.kt](mdc:app/src/main/java/com/example/myshujuguanli/ui/screens/ManagementScreen.kt))
   - 风险控制功能
   - 数据统计与分析

3. **数据管理界面**([DataManageScreen.kt](mdc:app/src/main/java/com/example/myshujuguanli/ui/screens/DataManageScreen.kt))
   - 对账功能
   - 数据导出与归档

4. **设置界面**([SettingsScreen.kt](mdc:app/src/main/java/com/example/myshujuguanli/ui/screens/SettingsScreen.kt))
   - 主题设置
   - 应用偏好

## 安全与激活

从[SecurityUtils.kt](mdc:app/src/main/java/com/example/myshujuguanli/utils/SecurityUtils.kt)迁移:

- 设备激活机制
- 服务器通信与状态验证
- 加密与解密操作

Swift版本应使用:
- KeychainAccess存储敏感信息
- CryptoKit进行加密操作
- URLSession进行网络请求

## 数据解析引擎

数据解析是核心功能，需从以下文件迁移:

- [DataParser.kt](mdc:app/src/main/java/com/example/myshujuguanli/utils/DataParser.kt)
- [ParseLearningEngine.kt](mdc:app/src/main/java/com/example/myshujuguanli/utils/ParseLearningEngine.kt)

实现要点:
- 正则表达式匹配规则
- 数据提取与处理逻辑
- 学习引擎的模式识别

## 服务器交互

iOS应用需要与现有服务器通信:

- [server.js](mdc:server.js)定义了API接口
- 实现相同的身份验证机制
- 维持相同的数据交换格式

## 适配策略

1. 创建共享代码：可考虑使用Kotlin Multiplatform优化代码复用
2. 先实现核心功能：数据解析、存储和基本UI
3. 再添加高级功能：数据导出、风险管理等
4. 最后优化iOS特有体验：支持Face ID、iCloud同步等

## 性能优化

- 对大量数据处理进行异步操作
- 利用Grand Central Dispatch进行并发处理
- 考虑使用Swift Concurrency(async/await)

## 测试策略

创建相同的测试数据集，确保iOS版本与Android版本产生一致的结果。
