---
description: 
globs: 
alwaysApply: false
---
# 开发工作流程

## 代码风格约定

### Android客户端

- 使用Kotlin编程语言
- 遵循MVVM架构模式
- UI使用Jetpack Compose构建
- 文件和类命名使用驼峰式(CamelCase)
- 包结构按功能模块组织

### 服务器端

- 使用CommonJS模块系统
- 函数优先使用异步方式(async/await)
- 错误处理使用try/catch块
- 数据库操作使用连接池管理连接

## 开发环境设置

### 客户端开发环境

- 使用Android Studio进行开发
- Gradle版本：见[gradle/wrapper/gradle-wrapper.properties](mdc:gradle/wrapper/gradle-wrapper.properties)
- SDK版本：见[app/build.gradle.kts](mdc:app/build.gradle.kts)

### 服务器开发环境

- Node.js版本要求：>=16.14.2
- 使用npm管理依赖
- 开发命令：`npm run dev`
- 启动命令：`npm start`

## 构建和部署

### 客户端构建

- 开发构建：使用Android Studio构建
- 发布构建：`./gradlew assembleRelease`

### 服务器部署

- 生产环境使用process manager管理Node.js进程
- 本地测试使用`node server.js`启动服务

## 依赖说明

- 客户端依赖：见[app/build.gradle.kts](mdc:app/build.gradle.kts)
- 服务器依赖：见[package.json](mdc:package.json)
