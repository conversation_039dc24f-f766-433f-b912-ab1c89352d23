---
description: 
globs: 
alwaysApply: false
---
# 数据模型

## 客户端数据模型

客户端主要在 [app/src/main/java/com/example/myshujuguanli/models/](mdc:app/src/main/java/com/example/myshujuguanli/models) 和 [app/src/main/java/com/example/myshujuguanli/model/](mdc:app/src/main/java/com/example/myshujuguanli/model) 目录中定义数据模型。

主要模型包括：
- 用户数据模型
- 设置数据模型
- 验证状态模型
- 设备信息模型

## 服务器端数据模型

服务器端使用SQLite数据库，主要包含以下表结构：

### devices 表
存储设备信息和激活状态

主要字段：
- deviceId: 设备唯一标识
- activationKey: 激活密钥
- status: 激活状态
- expiryDate: 过期时间
- lastCheckTime: 最后检查时间

### configs 表
存储系统配置信息

主要字段：
- key: 配置键
- value: 配置值
- description: 配置描述

### request_logs 表
存储API请求日志

主要字段：
- timestamp: 请求时间
- ip: 请求IP
- endpoint: 请求端点
- status: 请求状态码
- response_time: 响应时间

## 数据库文件

系统使用的本地数据库文件：
- [lottery.db](mdc:lottery.db): 主数据库
- [注额管理.db](mdc:注额管理.db): 额度管理数据库
