import express from 'express';
import sqlite3 from 'sqlite3';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import morgan from 'morgan';
import cors from 'cors';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import crypto from 'crypto';
import NodeCache from 'node-cache';
import prometheus from 'prom-client';
import hpp from 'hpp';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const port = process.env.PORT || 3000;

// 添加代理信任设置
app.set('trust proxy', 1);

// 安全中间件
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'"],
            fontSrc: ["'self'"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"],
        },
    },
    crossOriginEmbedderPolicy: true,
    crossOriginOpenerPolicy: true,
    crossOriginResourcePolicy: { policy: "same-site" },
    dnsPrefetchControl: true,
    frameguard: { action: "deny" },
    hidePoweredBy: true,
    hsts: true,
    ieNoOpen: true,
    noSniff: true,
    referrerPolicy: { policy: "strict-origin-when-cross-origin" },
    xssFilter: true
}));

// 防止参数污染
app.use(hpp());

app.use(cors());

// 添加 JSON 解析中间件
app.use(express.json());

app.use(morgan((tokens, req, res) => {
    const date = new Date();
    const time = date.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
    return [
        tokens['remote-addr'](req, res),
        '-',
        tokens['remote-user'](req, res),
        `[${time}]`,
        `"${tokens['method'](req, res)} ${tokens['url'](req, res)} HTTP/${tokens['http-version'](req, res)}"`,
        tokens['status'](req, res),
        tokens['res'](req, res, 'content-length'),
        `"${tokens['referrer'](req, res)}"`,
        `"${tokens['user-agent'](req, res)}"`
    ].join(' ');
}));

// 使用原生的 Date 对象，但设置正确的时区
process.env.TZ = 'Asia/Shanghai';

// 记录设备请求日志的中间件
app.use((req, res, next) => {
    const logEntry = {
        method: req.method,
        url: req.url,
        ip: req.ip,
        timestamp: new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })
    };
    console.log(logEntry);
    next();
});

// 更细粒度的速率限制
const apiLimiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 100,
    message: '请求过于频繁，请稍后再试'
});

const authLimiter = rateLimit({
    windowMs: 60 * 60 * 1000,
    max: 5,
    message: '验证失败次数过多，请稍后再试'
});

app.use('/api/', apiLimiter);
app.use('/api/activate', authLimiter);

// 使用原生的 sqlite3 连接池
const dbPool = {
    _pool: [],
    maxSize: 10,
    minSize: 2,
    
    async acquire() {
        if (this._pool.length > 0) {
            return this._pool.pop();
        }
        return new sqlite3.Database('devices.db');
    },
    
    async release(connection) {
        if (this._pool.length < this.maxSize) {
            this._pool.push(connection);
        } else {
            await this.destroy(connection);
        }
    },
    
    async destroy(connection) {
        return new Promise((resolve, reject) => {
            connection.close((err) => {
                if (err) reject(err);
                else resolve();
            });
        });
    }
};

// 数据库操作包装函数
async function dbOperation(operation) {
    const connection = await dbPool.acquire();
    try {
        return await operation(connection);
    } finally {
        await dbPool.release(connection);
    }
}

// 连接数据库
const db = new sqlite3.Database('devices.db');

// 在需要时调用一次，然后注释掉
// dropAllTables(db).then(() => {
//     console.log('所有表已删');
// }).catch(err => {
//     console.error('删除表失败:', err);
// });
// 创建表
db.serialize(() => {
    db.run(`
        CREATE TABLE IF NOT EXISTS devices (
            device_id TEXT PRIMARY KEY,    -- 设备唯一标识
            device_name TEXT,              -- 设备名称
            device_info TEXT,              -- 设备信息
            register_time INTEGER,         -- 注册时间
            expiry_time INTEGER,           -- 到期时间
            activation_code TEXT,          -- 激活码
            is_activated INTEGER DEFAULT 0 -- 是否已激活
        )
    `);

    db.run(`
        CREATE TABLE IF NOT EXISTS configs (
            key TEXT PRIMARY KEY,
            value TEXT
        )
    `);

    // 创建日志表
    db.run(`
        CREATE TABLE IF NOT EXISTS request_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            device_id TEXT,                    -- 设备ID
            request_time INTEGER,              -- 请求时间（时间戳）
            request_type TEXT,                 -- 请求类型
            request_path TEXT,                 -- 请求路径
            request_params TEXT,               -- 请求参数
            response_code INTEGER,             -- 响应状态码
            FOREIGN KEY(device_id) REFERENCES devices(device_id)
        )
    `);
});

// 激活码相关常量
const ACTIVATION_KEY = "your_secret_key_here";  // 与客户端保持一致
const SALT = "H#k9P@mZ";  // 与客户端保持一致
const ITERATIONS = 10000;  // 与客户端保持一致
const KEY_LENGTH = 256;    // 与客户端保持一致

// 生成激活码的函数
function generateActivationCode(deviceId) {
    try {
        // 生成一个随机的 8 字节数据
        const randomBytes = crypto.randomBytes(8);
        
        // 获取当前时间戳（按天）
        const timeComponent = Math.floor(Date.now() / (24 * 60 * 60 * 1000));
        
        // 设备ID的最后4位
        const deviceComponent = deviceId.slice(-4);
        
        // 时间戳转16进制，取后4位
        const timeHex = timeComponent.toString(16).slice(-4).padStart(4, '0');
        
        // 随机字节转16进制，取前8位
        const randomHex = randomBytes.toString('hex').slice(0, 8);
        
        // 组合成激活码 (XXXX-XXXX-XXXX-XXXX)
        const activationCode = [
            deviceComponent,
            timeHex,
            randomHex.slice(0, 4),
            randomHex.slice(4, 8)
        ].join('-');
        
        console.log('生成激活码:', {
            deviceId,
            activationCode,
            timeComponent,
            deviceComponent,
            timeHex,
            randomHex
        });
        
        return activationCode;
        
    } catch (error) {
        console.error('生成激活码失败:', error);
        throw error;
    }
}

// 记录请求日志到数据库的函数
async function logRequest(db, deviceId, requestType, requestPath, requestParams, responseCode) {
    const timestamp = Date.now(); // 使用毫秒时间戳
    return new Promise((resolve, reject) => {
        db.run(
            `INSERT INTO request_logs (
                device_id, 
                request_time, 
                request_type, 
                request_path, 
                request_params, 
                response_code
            ) VALUES (?, ?, ?, ?, ?, ?)`,
            [
                deviceId,
                timestamp,
                requestType,
                requestPath,
                typeof requestParams === 'string' ? requestParams : JSON.stringify(requestParams),
                responseCode
            ],
            (err) => {
                if (err) {
                    console.error('记录日志失败:', err);
                    reject(err);
                } else {
                    resolve();
                }
            }
        );
    });
}

// 日志中间件
app.use(async (req, res, next) => {
    const startTime = Date.now();
    const deviceId = req.body?.deviceId || req.query?.deviceId;
    
    // 捕获响应
    const originalSend = res.send;
    res.send = function (data) {
        const responseBody = JSON.parse(data);
        const responseCode = res.statusCode;
        
        // 记录请求日志
        logRequest(
            db,
            deviceId,
            req.method,
            req.path,
            {
                query: req.query,
                body: req.body,
                headers: req.headers
            },
            responseCode
        ).catch(err => console.error('记录日志失败:', err));

        originalSend.apply(res, arguments);
    };
    
    next();
});

// 修改生成激活码接口
app.post('/api/admin/generate-code', async (req, res) => {
    console.log('收到激活码请求:', req.body);
    const { deviceId } = req.body;
    
    try {
        // 检查设备是否存在
        const device = await new Promise((resolve, reject) => {
            db.get('SELECT * FROM devices WHERE device_id = ?', [deviceId], (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });

        if (!device) {
            console.log('设备不存在:', deviceId);
            return res.status(404).json({
                success: false,
                message: '设备不存在'
            });
        }

        // 生成激活码
        const activationCode = generateActivationCode(deviceId);
        console.log('成功生成激活码:', activationCode);

        // 更新设备的激活码
        await new Promise((resolve, reject) => {
            db.run(
                'UPDATE devices SET activation_code = ? WHERE device_id = ?',
                [activationCode, deviceId],
                (err) => {
                    if (err) reject(err);
                    else resolve();
                }
            );
        });

        // 返回成功响应
        res.json({
            success: true,
            message: '激活码生成成功',
            deviceId: deviceId,
            activationCode: activationCode
        });

    } catch (error) {
        console.error('生成激活码失败:', error);
        res.status(500).json({
            success: false,
            message: '生成激活码失败: ' + error.message
        });
    }
});

// 获取日志的路由
app.get('/api/admin/logs', async (req, res) => {
    try {
        // 获取查询参数
        const { date } = req.query;
        
        let query = `
            SELECT 
                request_logs.*,
                devices.device_name
            FROM request_logs
            LEFT JOIN devices ON request_logs.device_id = devices.device_id
        `;
        
        const params = [];
        
        // 如果有日期参数，添加日期过滤
        if (date) {
            const startTime = new Date(date);
            startTime.setHours(0, 0, 0, 0);
            const endTime = new Date(date);
            endTime.setHours(23, 59, 59, 999);
            
            query += ` WHERE request_time BETWEEN ? AND ?`;
            params.push(startTime.getTime(), endTime.getTime());
        }
        
        // 添加排序和限制
        query += ` ORDER BY request_time DESC LIMIT 1000`;

        const logs = await new Promise((resolve, reject) => {
            db.all(query, params, (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });

        // 格式化日志数据
        const formattedLogs = logs.map(log => ({
            ...log,
            request_time: log.request_time, // 保持时间戳格式
            request_params: typeof log.request_params === 'string' 
                ? log.request_params 
                : JSON.stringify(log.request_params)
        }));

        res.json({
            success: true,
            logs: formattedLogs
        });
    } catch (error) {
        console.error('获取日志错误:', error);
        res.status(500).json({
            success: false,
            message: '获取日志失败'
        });
    }
});

// 修改默认客服信息的定义
let SERVICE_INFO = {
    qq: "",
    weixin: "",
    phone: "",
    description: "现推广优惠价！！！7天100 15天180 30天350 试用已到期，请联系服订或购买活码继续使用"
};

// 在服务器启动时从数据库加载客服信息
async function loadServiceInfo() {
    try {
        const info = await new Promise((resolve, reject) => {
            db.get('SELECT value FROM configs WHERE key = ?', ['service_info'], (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });
        
        if (info && info.value) {
            SERVICE_INFO = JSON.parse(info.value);
            console.log('已从数据库加载客服信息:', SERVICE_INFO);
        } else {
            // 如果数据库中没有客服信息，则保存默认值
            await new Promise((resolve, reject) => {
                db.run(
                    'INSERT OR REPLACE INTO configs (key, value) VALUES (?, ?)',
                    ['service_info', JSON.stringify(SERVICE_INFO)],
                    (err) => {
                        if (err) reject(err);
                        else resolve();
                    }
                );
            });
            console.log('已保存默认客服信息到数据库');
        }
    } catch (error) {
        console.error('加载客服信息失败:', error);
    }
}

// 修改更新客服信息的接口
app.post('/api/admin/update-service-info', async (req, res) => {
    const { serviceInfo } = req.body;
    try {
        // 更新内存中的客服信息
        SERVICE_INFO = { ...SERVICE_INFO, ...serviceInfo };
        
        // 保存到数据库
        await new Promise((resolve, reject) => {
            db.run(
                'INSERT OR REPLACE INTO configs (key, value) VALUES (?, ?)',
                ['service_info', JSON.stringify(SERVICE_INFO)],
                (err) => {
                    if (err) reject(err);
                    else resolve();
                }
            );
        });

        console.log('客服信息已更新并保存到数据库:', SERVICE_INFO);

        res.json({
            success: true,
            message: '客服信息已更新',
            serviceInfo: SERVICE_INFO
        });
    } catch (error) {
        console.error('更新客服信息失败:', error);
        res.status(500).json({
            success: false,
            message: '更新客服信息失败: ' + error.message
        });
    }
});

// 修改获取客服信息的接口
app.get('/api/service-info', async (req, res) => {
    try {
        const info = await new Promise((resolve, reject) => {
            db.get('SELECT value FROM configs WHERE key = ?', ['service_info'], (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });

        if (info && info.value) {
            const serviceInfo = JSON.parse(info.value);
            res.json({
                success: true,
                serviceInfo: serviceInfo
            });
        } else {
            res.json({
                success: true,
                serviceInfo: SERVICE_INFO
            });
        }
    } catch (error) {
        console.error('获取客服信息失败:', error);
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// // 在服务器启动时加载客服信息
// app.listen(port, async () => {
//     console.log(`服务器运行在 http://localhost:${port}`);
//     await loadServiceInfo();
// });

// 添加订阅配置
const SUBSCRIPTION_PLANS = {
    'week': {
        name: '周卡',
        days: 7
    },
    'biweek': {
        name: '双周卡', 
        days: 15
    },
    'month': {
        name: '月卡',
        days: 30
    },
    'quarter': {
        name: '季卡',
        days: 90
    },
    'halfyear': {
        name: '半年卡',
        days: 180
    },
    'year': {
        name: '年卡',
        days: 365
    }
};

// 注册设备
app.post('/api/register', async (req, res) => {
    console.log('收到注册请求:', req.body);
    const { deviceId, deviceInfo } = req.body;
    const now = Date.now();

    // 开始事务
    db.serialize(() => {
        db.run('BEGIN TRANSACTION');

        try {
            // 先检查设备状态
            db.get('SELECT * FROM devices WHERE device_id = ?', [deviceId], (err, device) => {
                if (err) {
                    db.run('ROLLBACK');
                    throw err;
                }

                // 如果设备存在且已过期
                if (device && !device.is_activated && (!device.expiry_time || device.expiry_time < now)) {
                    db.run('COMMIT');
                    return res.status(403).json({
                        success: false,
                        message: '设备已过期',
                        status: 'EXPIRED',
                        deviceId,
                        expiryTime: device.expiry_time,
                        serviceInfo: SERVICE_INFO
                    });
                }

                // 如果设备存在，更新设备信息
                if (device) {
                    db.run(
                        'UPDATE devices SET device_info = ? WHERE device_id = ?',
                        [deviceInfo, deviceId],
                        (err) => {
                            if (err) {
                                db.run('ROLLBACK');
                                throw err;
                            }

                            db.run('COMMIT');
                            console.log('设备已存在，已更新信息:', deviceId);
                            res.json({
                                success: true,
                                deviceId,
                                deviceInfo,
                                isActivated: device.is_activated === 1,
                                expiryTime: device.expiry_time,
                                activationCode: device.activation_code,
                                serviceInfo: SERVICE_INFO,
                                status: getDeviceStatus(device)
                            });
                        }
                    );
                } else {
                    // 新设备注册
                    const trialPeriod = 7 * 24 * 60 * 60 * 1000; // 7天试用期
                    const expiryTime = now + trialPeriod;

                    db.run(
                        'INSERT INTO devices (device_id, device_info, register_time, expiry_time, is_activated) VALUES (?, ?, ?, ?, 0)',
                        [deviceId, deviceInfo, now, expiryTime],
                        (err) => {
                            if (err) {
                                db.run('ROLLBACK');
                                throw err;
                            }

                            db.run('COMMIT');
                            console.log('新设备注册成功:', deviceId);
                            res.json({
                                success: true,
                                deviceId,
                                deviceInfo,
                                isActivated: false,
                                expiryTime: expiryTime,
                                activationCode: null,
                                serviceInfo: SERVICE_INFO,
                                status: 'TRIAL'
                            });
                        }
                    );
                }
            });
        } catch (error) {
            db.run('ROLLBACK');
            console.error('注册失败:', error);
            res.status(500).json({
                success: false,
                message: '注册失败: ' + error.message
            });
        }
    });
});

// 获设备列表
app.get('/api/devices', async (req, res) => {
    try {
        const devices = await new Promise((resolve, reject) => {
            db.all('SELECT * FROM devices', (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });

        res.json({
            success: true,
            devices: devices.map(device => ({
                ...device,
                registerTime: formatDateTime(device.register_time),
                expiryTime: device.is_activated ? '永久有效' : formatDateTime(device.expiry_time),
                status: getDeviceStatus(device)
            }))
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: '获取设备列表失败'
        });
    }
});

// 添加设备状态判断函数
function getDeviceStatus(device) {
    const now = Date.now();
    
    if (device.is_activated === 1) {
        return 'ACTIVATED';  // 改为使用枚举值
    }
    
    if (!device.expiry_time || device.expiry_time < now) {
        return 'EXPIRED';    // 改为使用枚举值
    }
    
    return 'TRIAL';         // 改为使用枚举值
}

// 删除设备
app.post('/api/admin/delete-device', async (req, res) => {
    const { deviceId } = req.body;
    try {
        await dbRun('DELETE FROM devices WHERE device_id = ?', [deviceId]);
        res.json({
            success: true,
            message: '设备已删除'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: '删除设备失败'
        });
    }
});

// 更新到期时间
app.post('/api/admin/update-expiry', async (req, res) => {
    const { deviceId, duration } = req.body;
    try {
        const device = await dbGet('SELECT * FROM devices WHERE device_id = ?', [deviceId]);
        if (!device) {
            return res.status(404).json({
                success: false,
                message: '设备不存在'
            });
        }

        const now = Date.now();
        const newExpiryTime = Math.max(now, device.expiry_time) + duration;
        
        await dbRun(
            'UPDATE devices SET expiry_time = ? WHERE device_id = ?',
            [newExpiryTime, deviceId]
        );

        res.json({
            success: true,
            message: '到期时间已更新',
            expiryTime: newExpiryTime
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: '更新到期时间失败'
        });
    }
});

// 验证激活码
function verifyActivationCode(deviceId, activationCode) {
    try {
        // 移除横杠并转换大写
        const cleanCode = activationCode.replace(/-/g, '').toUpperCase();
        if (cleanCode.length !== 16) return false;
        
        // 验证设备ID部分
        const deviceComponent = deviceId.slice(-4).toUpperCase();
        if (cleanCode.slice(0, 4) !== deviceComponent) return false;
        
        // 生成当前和前后几天的激活码进行比对
        const now = Math.floor(Date.now() / (24 * 60 * 60 * 1000));
        for (let dayOffset = -1; dayOffset <= 1; dayOffset++) {
            const time = now + dayOffset;
            const expectedCode = generateActivationCode(deviceId);
            if (expectedCode.replace(/-/g, '').toUpperCase() === cleanCode) {
                return true;
            }
        }
        
        return false;
    } catch (error) {
        console.error('验证激活失败:', error);
        return false;
    }
}



// 添加激活接口
app.post('/api/activate', async (req, res) => {
    console.log('收到激活请求:', req.body);
    const { deviceId, activationCode, deviceInfo } = req.body;
    
    try {
        // 1. 检查设备是否存在
        const device = await new Promise((resolve, reject) => {
            db.get('SELECT * FROM devices WHERE device_id = ?', [deviceId], (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });

        if (!device) {
            console.log('设备不存在:', deviceId);
            return res.status(404).json({
                success: false,
                message: '设备不存在'
            });
        }

        // 2. 检查设备是否已永久激活
        if (device.is_activated) {
            console.log('设备已永久激活:', deviceId);
            return res.json({
                success: true,
                message: '设备已永久激活',
                isActivated: true
            });
        }

        // 3. 检查激活码
        if (!device.activation_code) {
            console.log('设备没有可用的激活码:', deviceId);
            return res.status(400).json({
                success: false,
                message: '激活码无效或已使用'
            });
        }

        if (device.activation_code !== activationCode) {
            console.log('激活码不匹配:', {
                expected: device.activation_code,
                received: activationCode
            });
            return res.status(400).json({
                success: false,
                message: '激活码无效'
            });
        }

        // 4. 激活成功，更新设备状态为永久激活
        await new Promise((resolve, reject) => {
            db.run(
                'UPDATE devices SET is_activated = 1, activation_code = NULL, expiry_time = NULL WHERE device_id = ?',
                [deviceId],
                (err) => {
                    if (err) reject(err);
                    else resolve();
                }
            );
        });

        console.log('永久激活成功:', {
            deviceId,
            activationCode
        });

        res.json({
            success: true,
            message: '永久激活成功',
            isActivated: true
        });

    } catch (error) {
        console.error('激活失败:', error);
        res.status(500).json({
            success: false,
            message: '激活失败: ' + error.message
        });
    }
});

// 添加订阅接口
app.post('/api/admin/subscribe', async (req, res) => {
    const { deviceId, planType } = req.body;
    try {
        const device = await dbGet('SELECT * FROM devices WHERE device_id = ?', [deviceId]);
        if (!device) {
            return res.status(404).json({
                success: false,
                message: '设备不存在'
            });
        }

        const plan = SUBSCRIPTION_PLANS[planType.toLowerCase()];
        if (!plan) {
            return res.status(400).json({
                success: false,
                message: '无效的订阅类型'
            });
        }

        const now = Date.now();
        const duration = plan.days * 24 * 60 * 60 * 1000;
        const newExpiryTime = Math.max(now, device.expiry_time || now) + duration;

        await dbRun(`
            UPDATE devices SET 
            expiry_time = ?,
            activation_code = ?,
            register_time = CASE 
                WHEN register_time IS NULL THEN ? 
                ELSE register_time 
            END
            WHERE device_id = ?
        `, [newExpiryTime, device.activation_code, now, deviceId]);

        res.json({
            success: true,
            message: `订阅成功: ${plan.name}`,
            deviceId: deviceId,
            planType: planType,
            planName: plan.name,
            duration: duration,
            expiryTime: newExpiryTime,
            remainingTime: newExpiryTime - now
        });

    } catch (error) {
        console.error('订阅失败:', error);
        res.status(500).json({
            success: false,
            message: '订阅失败'
        });
    }
});

// 1. 首先定义缓存实现
const cache = {
    _store: new Map(),
    _timeouts: new Map(),
    
    set(key, value, ttl) {
        if (this._timeouts.has(key)) {
            clearTimeout(this._timeouts.get(key));
        }
        
        this._store.set(key, value);
        this._timeouts.set(key, setTimeout(() => {
            this._store.delete(key);
            this._timeouts.delete(key);
        }, ttl * 1000));
    },
    
    get(key) {
        return this._store.get(key);
    },
    
    clear() {
        this._store.clear();
        this._timeouts.forEach(timeout => clearTimeout(timeout));
        this._timeouts.clear();
    }
};

// 2. 定义缓存中间件
const cacheMiddleware = (duration) => {
    return (req, res, next) => {
        const key = req.originalUrl || req.url;
        const cachedResponse = cache.get(key);
        
        if (cachedResponse) {
            return res.json(cachedResponse);
        }

        const originalJson = res.json;
        res.json = function(body) {
            cache.set(key, body, duration);
            return originalJson.call(this, body);
        };
        
        next();
    };
};

// 3. 然后再定义使用缓存的路由
app.get('/api/subscription-plans', cacheMiddleware(300), async (req, res) => {
    try {
        const plans = Object.entries(SUBSCRIPTION_PLANS).map(([type, plan]) => ({
            type,
            name: plan.name,
            days: plan.days,
            description: `${plan.name} (${plan.days}天)`,
            features: [
                `有效期${plan.days}天`,
                '无限次数使用',
                '技术支持服务'
            ]
        }));

        res.json({
            success: true,
            plans,
            serviceInfo: SERVICE_INFO
        });
    } catch (error) {
        console.error('获取订阅计划失败:', error);
        res.status(500).json({
            success: false,
            message: '获取订阅计划失败'
        });
    }
});

// 修改设备信息接口
app.post('/api/admin/update-device', async (req, res) => {
    const { deviceId, deviceName, expiryTime } = req.body;
    console.log('收到更新设备请求:', {
        deviceId,
        deviceName,
        expiryTime,
        currentTime: Date.now()
    });

    try {
        const device = await dbGet('SELECT * FROM devices WHERE device_id = ?', [deviceId]);
        console.log('当前设备信息:', device);

        if (!device) {
            return res.status(404).json({
                success: false,
                message: '设备不存在'
            });
        }

        const updates = [];
        const params = [];

        if (deviceName !== undefined) {
            updates.push('device_name = ?');
            params.push(deviceName);
        }

        if (expiryTime !== undefined) {
            console.log('更新到期时间:', {
                oldExpiryTime: device.expiry_time,
                newExpiryTime: expiryTime,
                difference: expiryTime - device.expiry_time
            });
            updates.push('expiry_time = ?');
            params.push(expiryTime);
        }

        if (updates.length === 0) {
            return res.json({
                success: true,
                message: '没有需要更新的内容'
            });
        }

        params.push(deviceId);
        await dbRun(
            `UPDATE devices SET ${updates.join(', ')} WHERE device_id = ?`,
            params
        );

        // 获取更新后的设备信息
        const updatedDevice = await dbGet('SELECT * FROM devices WHERE device_id = ?', [deviceId]);
        console.log('更新后的设备信息:', updatedDevice);
        console.log('设备状态:', getDeviceStatus(updatedDevice));

        res.json({
            success: true,
            message: '设备信息已更新',
            device: {
                ...updatedDevice,
                registerTime: formatDateTime(updatedDevice.register_time),
                expiryTime: formatDateTime(updatedDevice.expiry_time),
                status: getDeviceStatus(updatedDevice)
            }
        });

    } catch (error) {
        console.error('更新设备信息失败:', error);
        res.status(500).json({
            success: false,
            message: '更新设备信息失败'
        });
    }
});

// 工具函数
function formatDateTime(timestamp) {
    return new Date(timestamp).toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai'
    });
}

// 数据库操作包装函数
function dbRun(sql, params = []) {
    return dbOperation(db => {
        return new Promise((resolve, reject) => {
            db.run(sql, params, function(err) {
                if (err) reject(err);
                else resolve(this);
            });
        });
    });
}

function dbGet(sql, params = []) {
    return dbOperation(db => {
        return new Promise((resolve, reject) => {
            db.get(sql, params, (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });
    });
}

function dbAll(sql, params = []) {
    return dbOperation(db => {
        return new Promise((resolve, reject) => {
            db.all(sql, params, (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });
    });
}

// 添加获取客服息接口
app.get('/api/service-info', async (req, res) => {
    try {
        const info = await db.get('SELECT value FROM configs WHERE key = ?', ['service_info']);
        res.json({
            success: true,
            serviceInfo: info ? JSON.parse(info.value) : SERVICE_INFO
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// 启动服务器
app.listen(port, async () => {
    console.log(`服务器运行在 http://localhost:${port}`);
    await loadServiceInfo();
});

// 删除所有表的函数
async function dropAllTables(db) {
    return new Promise((resolve, reject) => {
        db.serialize(() => {
            db.run('BEGIN TRANSACTION');
            try {
                // 先删除有外键约束的表
                db.run(`DROP TABLE IF EXISTS request_logs`);
                // 再删除其他表
                db.run(`DROP TABLE IF EXISTS devices`);
                db.run(`DROP TABLE IF EXISTS configs`);
                db.run('COMMIT');
                resolve();
            } catch (error) {
                db.run('ROLLBACK');
                reject(error);
            }
        });
    });
}
// 在需要时调用一次，然后注释掉
// dropAllTables(db).then(() => {
//     console.log('所有表已删');
// }).catch(err => {
//     console.error('删除表失败:', err);
// });
// 添加重置数据库接口
app.post('/api/admin/reset-database', async (req, res) => {
    const { adminKey } = req.body;
    
    // 验证管理员密钥
    if (adminKey !== process.env.ADMIN_KEY) {
        return res.status(403).json({
            success: false,
            message: '没有权限执行此操作'
        });
    }

    try {
        // 删除所有表
        await dropAllTables(db);
        
        // 重新创建表
        await new Promise((resolve, reject) => {
            db.serialize(() => {
                // 创建设备表
                db.run(`
                    CREATE TABLE IF NOT EXISTS devices (
                        device_id TEXT PRIMARY KEY,
                        device_name TEXT,
                        device_info TEXT,
                        register_time INTEGER,
                        expiry_time INTEGER,
                        activation_code TEXT,
                        is_activated INTEGER DEFAULT 0
                    )
                `);

                // 创建配置表
                db.run(`
                    CREATE TABLE IF NOT EXISTS configs (
                        key TEXT PRIMARY KEY,
                        value TEXT
                    )
                `);

                // 创建日志表
                db.run(`
                    CREATE TABLE IF NOT EXISTS request_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        device_id TEXT,
                        request_time INTEGER,
                        request_type TEXT,
                        request_path TEXT,
                        request_params TEXT,
                        response_code INTEGER,
                        FOREIGN KEY(device_id) REFERENCES devices(device_id)
                    )
                `, (err) => {
                    if (err) reject(err);
                    else resolve();
                });
            });
        });

        res.json({
            success: true,
            message: '数据库已重置'
        });

    } catch (error) {
        console.error('重置数据库失败:', error);
        res.status(500).json({
            success: false,
            message: '重置数据库失败: ' + error.message
        });
    }
});

// 添加清理过期日志的接口
app.post('/api/admin/clean-logs', async (req, res) => {
    const { adminKey, days = 30 } = req.body;
    
    // 验证管理员密钥
    if (adminKey !== process.env.ADMIN_KEY) {
        return res.status(403).json({
            success: false,
            message: '没有权限执行此操作'
        });
    }

    try {
        const cutoffTime = Date.now() - (days * 24 * 60 * 60 * 1000);
        
        await dbRun(
            'DELETE FROM request_logs WHERE request_time < ?',
            [cutoffTime]
        );

        res.json({
            success: true,
            message: `已清理${days}天前的日志`
        });

    } catch (error) {
        console.error('清理日志失败:', error);
        res.status(500).json({
            success: false,
            message: '清理日志失败: ' + error.message
        });
    }
});

// 添加中间件来检查设备状态
app.use(async (req, res, next) => {
    const deviceId = req.body?.deviceId || req.query?.deviceId;
    if (deviceId) {
        try {
            const device = await dbGet('SELECT * FROM devices WHERE device_id = ?', [deviceId]);
            console.log('状态检查:', {
                deviceId,
                currentTime: Date.now(),
                device,
                isActivated: device?.is_activated,
                expiryTime: device?.expiry_time,
                status: device ? getDeviceStatus(device) : null
            });

            if (device && !device.is_activated) {
                const now = Date.now();
                if (!device.expiry_time || device.expiry_time < now) {
                    console.log('设备已过期:', {
                        deviceId,
                        expiryTime: device.expiry_time,
                        currentTime: now,
                        difference: device.expiry_time - now
                    });
                    return res.status(403).json({
                        success: false,
                        message: '设备已过期',
                        serviceInfo: SERVICE_INFO
                    });
                }
            }
        } catch (error) {
            console.error('检查设备状态失败:', error);
        }
    }
    next();
});

// 状态检查接口
app.post('/api/status', async (req, res) => {
    const serverTime = Date.now();
    const { deviceId, deviceInfo, localStatus } = req.body;
    
    try {
        let device = await dbGet('SELECT * FROM devices WHERE device_id = ?', [deviceId]);
        
        if (!device) {
            // 设备不存在,注册新设备
            const registerTime = localStatus?.registerTime || serverTime;
            const expiryTime = localStatus?.expiryTime || (serverTime + 7 * 24 * 60 * 60 * 1000);
            
            await dbRun(
                'INSERT INTO devices (device_id, device_info, register_time, expiry_time, is_activated) VALUES (?, ?, ?, ?, 0)',
                [deviceId, deviceInfo, registerTime, expiryTime]
            );
            
            device = await dbGet('SELECT * FROM devices WHERE device_id = ?', [deviceId]);
        }

        const status = getDeviceStatus(device);
        const remainingTime = device.expiry_time > serverTime ? device.expiry_time - serverTime : 0;
        const timeDescription = remainingTime > 0 
            ? formatDuration(remainingTime)
            : "已过期";

        // 统一返回格式，不管是否过期都返回完整的客服信息
        res.json({
            success: true,
            deviceId,
            status,
            serverTime,
            registerTime: device.register_time,
            expiryTime: device.expiry_time,
            remainingTime,
            timeDescription,
            isActivated: device.is_activated === 1,
            activationCode: device.activation_code,
            serviceInfo: SERVICE_INFO  // 始终返回完整的客服信息
        });

    } catch (error) {
        console.error('状态检查失败:', error);
        res.status(500).json({
            success: false,
            message: '状态检查失败'
        });
    }
});

// 添加时间格式化函数
function formatDuration(ms) {
    const days = Math.floor(ms / (24 * 60 * 60 * 1000));
    const hours = Math.floor((ms % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
    return days > 0 ? `${days}天${hours}小时` : `${hours}小时`;
}

// 统一错误理中间件
const errorHandler = (err, req, res, next) => {
    console.error('Error:', {
        message: err.message,
        stack: err.stack,
        timestamp: new Date().toISOString(),
        path: req.path,
        method: req.method,
        ip: req.ip
    });

    res.status(err.status || 500).json({
        success: false,
        message: process.env.NODE_ENV === 'production' 
            ? '服务器错误' 
            : err.message,
        error: process.env.NODE_ENV === 'production' 
            ? undefined 
            : err.stack
    });
};

app.use(errorHandler);

// 异步错误处理包装器
const asyncHandler = (fn) => (req, res, next) =>
    Promise.resolve(fn(req, res, next)).catch(next);

// 使用示例
app.post('/api/activate', asyncHandler(async (req, res) => {
    // 原有激活逻辑...
}));

// 初始化metrics
const collectDefaultMetrics = prometheus.collectDefaultMetrics;
collectDefaultMetrics({ timeout: 5000 });

// 请求计数器
const httpRequestDuration = new prometheus.Histogram({
    name: 'http_request_duration_seconds',
    help: 'Duration of HTTP requests in seconds',
    labelNames: ['method', 'route', 'status_code'],
    buckets: [0.1, 0.5, 1, 2, 5]
});

// 监控中间件
app.use((req, res, next) => {
    const start = Date.now();
    res.on('finish', () => {
        const duration = Date.now() - start;
        httpRequestDuration.observe(
            {
                method: req.method,
                route: req.route?.path || req.path,
                status_code: res.statusCode
            },
            duration / 1000
        );
    });
    next();
});

// 暴露metrics端点
app.get('/metrics', async (req, res) => {
    res.set('Content-Type', prometheus.register.contentType);
    res.end(await prometheus.register.metrics());
});

// 添加全局错误处理
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
});

// 优化日志记录中间件
const logMiddleware = async (req, res, next) => {
    const startTime = Date.now();
    const logEntry = {
        method: req.method,
        url: req.url,
        ip: req.ip,
        timestamp: new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })
    };

    // 包装响应对象
    const originalSend = res.send;
    res.send = function(data) {
        const responseTime = Date.now() - startTime;
        const logData = {
            ...logEntry,
            responseTime,
            statusCode: res.statusCode,
            response: typeof data === 'string' ? data : JSON.stringify(data)
        };
        
        // 异步记录日志
        setImmediate(() => {
            console.log('Request Log:', logData);
            logRequest(
                db,
                req.body?.deviceId || req.query?.deviceId,
                req.method,
                req.path,
                {
                    query: req.query,
                    body: req.body,
                    headers: req.headers
                },
                res.statusCode
            ).catch(err => console.error('记录日志失败:', err));
        });

        return originalSend.apply(res, arguments);
    };
    
    next();
};

app.use(logMiddleware);

// 优雅关闭处理
const shutdown = async () => {
    console.log('正在关闭服务器...');
    
    // 清理缓存
    cache.clear();
    
    // 关闭所有数据库连接
    for (const connection of dbPool._pool) {
        await dbPool.destroy(connection);
    }
    
    // 关闭主数据库连接
    await new Promise((resolve) => {
        db.close(() => {
            console.log('数据库连接已关闭');
            resolve();
        });
    });
    
    process.exit(0);
};

process.on('SIGTERM', shutdown);
process.on('SIGINT', shutdown);
