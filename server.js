const express = require('express');
const sqlite3 = require('sqlite3');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const morgan = require('morgan');
const cors = require('cors');
const path = require('path');
const crypto = require('crypto');
const prometheus = require('prom-client');
const hpp = require('hpp');
const fs = require('fs');
const bodyParser = require('body-parser');

const app = express();
const port = process.env.PORT || 3000;
const isDev = process.env.NODE_ENV !== 'production';

// 确保 .data 目录存在
const DATA_DIR = './.data';
const LOG_FILE = path.join(DATA_DIR, 'log.txt');
const DB_PATH = path.join(DATA_DIR, 'devices.db');

// 创建必要的目录和文件
if (!fs.existsSync(DATA_DIR)) {
    fs.mkdirSync(DATA_DIR);
}

// 添加代理信任设置
app.set('trust proxy', 1);

// 安全中间件
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'"],
            fontSrc: ["'self'"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"],
        },
    },
    crossOriginEmbedderPolicy: true,
    crossOriginOpenerPolicy: true,
    crossOriginResourcePolicy: { policy: "same-site" },
    dnsPrefetchControl: true,
    frameguard: { action: "deny" },
    hidePoweredBy: true,
    hsts: true,
    ieNoOpen: true,
    noSniff: true,
    referrerPolicy: { policy: "strict-origin-when-cross-origin" },
    xssFilter: true
}));

// 防止参数污染
app.use(hpp());

app.use(cors());

// 添加 JSON 解析中间件
app.use(express.json());

app.use(morgan((tokens, req, res) => {
    const date = new Date();
    const time = date.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
    return [
        tokens['remote-addr'](req, res),
        '-',
        tokens['remote-user'](req, res),
        `[${time}]`,
        `"${tokens['method'](req, res)} ${tokens['url'](req, res)} HTTP/${tokens['http-version'](req, res)}"`,
        tokens['status'](req, res),
        tokens['res'](req, res, 'content-length'),
        `"${tokens['referrer'](req, res)}"`,
        `"${tokens['user-agent'](req, res)}"`
    ].join(' ');
}));

// 使用原生的 Date 对象，但设置正确的时区
process.env.TZ = 'Asia/Shanghai';

// 创建写日志函数
function writeLog(message) {
    const timestamp = new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
    // 只记录关键信息，避免冗余
    const logMessage = `[${timestamp}] ${message}\n`;
    
    // 异步写入日志，不阻塞主流程
    fs.appendFile(LOG_FILE, logMessage, (err) => {
        if (err && err.code === 'ENOSPC') {
            // 如果空间不足，触发紧急维护
            maintainDatabase().catch(console.error);
        }
    });
    
    console.log(logMessage.trim());
}

// 更细粒度的速率限制
const apiLimiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 100,
    message: '请求过于频繁，请稍后再试'
});

const authLimiter = rateLimit({
    windowMs: 60 * 60 * 1000,
    max: 5,
    message: '验证失败次数过多，请稍后再试'
});

app.use('/api/', apiLimiter);
app.use('/api/activate', authLimiter);

// 使用原生的 sqlite3 连接池
const dbPool = {
    _pool: [],
    maxSize: 10,
    minSize: 2,
    
    async acquire() {
        if (this._pool.length > 0) {
            return this._pool.pop();
        }
        return new Promise((resolve, reject) => {
            const db = new sqlite3.Database(DB_PATH, (err) => {
                if (err) reject(err);
                else resolve(db);
            });
        });
    },
    
    async release(connection) {
        if (this._pool.length < this.maxSize) {
            this._pool.push(connection);
        } else {
            await this.destroy(connection);
        }
    },
    
    async destroy(connection) {
        return new Promise((resolve, reject) => {
            connection.close((err) => {
                if (err) reject(err);
                else resolve();
            });
        });
    }
};

// 修改数据库操作的日志记录
async function dbOperation(operation) {
    let connection;
    try {
        connection = await dbPool.acquire();
        return await operation(connection);
    } catch (error) {
        writeLog(`数据库操作失败: ${error.message}`);
        throw error;
    } finally {
        if (connection) {
            await dbPool.release(connection);
        }
    }
}

// 修改数据库连接
const db = new sqlite3.Database(DB_PATH, (err) => {
    if (err) {
        console.error('数据库连接失败:', err);
        process.exit(1);
    }
    console.log('数据库已连接:', DB_PATH);
    
    // 设置数据库参数
    db.run('PRAGMA journal_mode = DELETE'); // 改用 DELETE 模式避免生成额外文件
    db.run('PRAGMA auto_vacuum = FULL');
    db.run('PRAGMA page_size = 4096');
});

// 修改删除表函数
async function dropAllTables(db) {
    return new Promise((resolve, reject) => {
        db.serialize(() => {
            try {
                // 1. 先删除所有表
                db.run('DROP TABLE IF EXISTS request_logs');
                db.run('DROP TABLE IF EXISTS devices');
                db.run('DROP TABLE IF EXISTS configs');
                
                // 2. 执行 VACUUM（在事务外执行）
                db.run('VACUUM', (err) => {
                    if (err) {
                        console.error('VACUUM 失败:', err);
                        reject(err);
                        return;
                    }
                    
                    // 3. 重新创建表
                    db.run(`
                        CREATE TABLE IF NOT EXISTS devices (
                            device_id TEXT PRIMARY KEY,
                            device_name TEXT,
                            device_info TEXT,
                            register_time INTEGER,
                            expiry_time INTEGER,
                            is_activated INTEGER DEFAULT 0,
                            last_request_time INTEGER,
                            subscription_type TEXT,
                            device_signature TEXT,
                            hardware_info TEXT,
                            last_verify_time INTEGER,
                            status TEXT DEFAULT 'TRIAL'
                        )
                    `);

                    db.run(`
                        CREATE TABLE IF NOT EXISTS configs (
                            key TEXT PRIMARY KEY,
                            value TEXT
                        )
                    `);

                    db.run(`
                        CREATE TABLE IF NOT EXISTS request_logs (
                            device_id TEXT PRIMARY KEY,
                            request_time INTEGER,
                            request_type TEXT,
                            request_path TEXT
                        )
                    `, (err) => {
                        if (err) {
                            console.error('创建表失败:', err);
                            reject(err);
                        } else {
                            console.log('所有表重建完成');
                            resolve();
                        }
                    });
                });
            } catch (error) {
                console.error('删除表失败:', error);
                reject(error);
            }
        });
    });
}

// 修改数据库维护函数
async function maintainDatabase() {
    try {
        const now = Date.now();
        
        // 1. 优化请求日志表 - 只保留每个设备最新的一条记录
        await dbRun(`
            DELETE FROM request_logs 
            WHERE rowid NOT IN (
                SELECT MAX(rowid) 
                FROM request_logs 
                GROUP BY device_id
            )
        `);

        // 2. 归档不活跃设备而不是删除
        await dbRun(`
            UPDATE devices 
            SET 
                device_info = json_set(
                    COALESCE(device_info, '{}'),
                    '$.archived', 1,
                    '$.archive_time', ?,
                    '$.archive_reason', 'inactive'
                )
            WHERE 
                is_activated = 0 
                AND expiry_time < ? 
                AND last_request_time < ?
                AND json_extract(device_info, '$.archived') IS NULL
        `, [
            now,
            now,
            now - 30 * 24 * 60 * 60 * 1000 // 30天未活动
        ]);

        // 3. 压缩日志文件 - 只保留最近7天的日志
        try {
            const logs = await fs.promises.readFile(LOG_FILE, 'utf8');
            const logLines = logs.split('\n').filter(line => {
                const match = line.match(/\[(.*?)\]/);
                if (!match) return false;
                const logTime = new Date(match[1]).getTime();
                return (now - logTime) < 7 * 24 * 60 * 60 * 1000;
            });
            await fs.promises.writeFile(LOG_FILE, logLines.join('\n'));
        } catch (err) {
            console.error('压缩日志文件失败:', err);
        }

        // 4. 执行数据库VACUUM优化
        await dbRun('VACUUM');
        
        writeLog('数据库维护完成');
    } catch (error) {
        writeLog(`数据库维护失败: ${error.message}`);
    }
}

// 增加维护频率，每6小时执行一次
setInterval(maintainDatabase, 6 * 60 * 60 * 1000);

// 修改日志记录函数，优化日志内容
function writeLog(message) {
    const timestamp = new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
    // 只记录关键信息，避免冗余
    const logMessage = `[${timestamp}] ${message}\n`;
    
    // 异步写入日志，不阻塞主流程
    fs.appendFile(LOG_FILE, logMessage, (err) => {
        if (err && err.code === 'ENOSPC') {
            // 如果空间不足，触发紧急维护
            maintainDatabase().catch(console.error);
        }
    });
    
    console.log(logMessage.trim());
}

// 修改请求日志中间件，优化存储
app.use((req, res, next) => {
    const deviceId = req.body?.deviceId || req.query?.deviceId;
    if (!deviceId) return next();

    // 包装响应对象
    const originalSend = res.send;
    res.send = function(data) {
        // 异步记录日志，只记录关键信息
        setImmediate(() => {
            dbRun(
                `INSERT OR REPLACE INTO request_logs (
                    device_id, 
                    request_time,
                    request_type,
                    request_path
                ) VALUES (?, ?, ?, ?)`,
                [
                    deviceId,
                    Date.now(),
                    req.method,
                    req.path
                ]
            ).catch(err => writeLog(`记录日志失败: ${err.message}`));
        });

        return originalSend.apply(res, arguments);
    };
    
    next();
});

// 修改激活接口为直接永久激活
app.post('/api/activate', async (req, res) => {
    writeLog('收到激活请求:');
    const { deviceId } = req.body;
    
    try {
        // 1. 检查设备是否存在
        const device = await dbGet('SELECT * FROM devices WHERE device_id = ?', [deviceId]);

        if (!device) {
            writeLog(`设备不存在: ${deviceId}`);
            return res.status(404).json({
                success: false,
                message: '设备不存在'
            });
        }

        // 2. 检查设备是否已永久激活
        if (device.is_activated === 1) {
            writeLog(`设备已永久激活: ${deviceId}`);
            return res.json({
                success: true,
                message: '设备已永久激活',
                status: 'ACTIVATED'
            });
        }

        // 3. 永久激活设备
        await dbRun(
            'UPDATE devices SET status = ?, is_activated = ?, expiry_time = ? WHERE device_id = ?',
            ['ACTIVATED', 1, 0, deviceId]  // expiry_time 设为 0 表示永久激活
        );

        writeLog(`永久激活成功: ${deviceId}`);

        res.json({
            success: true,
            message: '永久激活成功',
            status: 'ACTIVATED',
            deviceId: deviceId,
            expiryTime: 0  // 永久激活的到期时间为0
        });

    } catch (error) {
        writeLog(`激活失败: ${error.message}`);
        res.status(500).json({
            success: false,
            message: '激活失败'
        });
    }
});

// 获取日志的路由
app.get('/api/admin/logs', async (req, res) => {
    try {
        // 获取查询参数
        const { date } = req.query;
        
        let query = `
            SELECT 
                request_logs.*,
                devices.device_name
            FROM request_logs
            LEFT JOIN devices ON request_logs.device_id = devices.device_id
        `;
        
        const params = [];
        
        // 如果有日期参数，添加日期过滤
        if (date) {
            const startTime = new Date(date);
            startTime.setHours(0, 0, 0, 0);
            const endTime = new Date(date);
            endTime.setHours(23, 59, 59, 999);
            
            query += ` WHERE request_time BETWEEN ? AND ?`;
            params.push(startTime.getTime(), endTime.getTime());
        }
        
        // 添加排序和限制
        query += ` ORDER BY request_time DESC LIMIT 1000`;

        const logs = await new Promise((resolve, reject) => {
            db.all(query, params, (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });

        // 格式化日志数据
        const formattedLogs = logs.map(log => ({
            ...log,
            request_time: log.request_time, // 保持时间戳格式
            request_params: typeof log.request_params === 'string' 
                ? log.request_params 
                : JSON.stringify(log.request_params)
        }));

        res.json({
            success: true,
            logs: formattedLogs
        });
    } catch (error) {
        writeLog(`获取日志错误: ${error.message}`);
        res.status(500).json({
            success: false,
            message: '获取日志失败'
        });
    }
});

// 修改默认客服信息的定义
let SERVICE_INFO = {
    qq: "",
    weixin: "",
    phone: "",
    description: "现推广优惠价！！！7天99 15天168 30天288 请联系客服订阅或激活继续使用"
};

// 在服务器启动时从数据库加载客服信息
async function loadServiceInfo() {
    try {
        const info = await dbGet('SELECT value FROM configs WHERE key = ?', ['service_info']);
        
        if (info && info.value) {
            SERVICE_INFO = JSON.parse(info.value);
            writeLog('已从数据库加载客服信息:');
            console.log(SERVICE_INFO);
        } else {
            // 如果数据库中没有客服信息，则保存默认值
            await dbRun(
                'INSERT OR REPLACE INTO configs (key, value) VALUES (?, ?)',
                ['service_info', JSON.stringify(SERVICE_INFO)]
            );
            writeLog('已保存默认客服信息到数据库');
            console.log(SERVICE_INFO);
        }
        return SERVICE_INFO;
    } catch (error) {
        writeLog(`加载客服信息失败: ${error.message}`);
        return SERVICE_INFO; // 返回默认值
    }
}

// 修改获取客服信息的函数
async function getServiceInfo() {
    try {
        const info = await dbGet('SELECT value FROM configs WHERE key = ?', ['service_info']);
        if (info && info.value) {
            return JSON.parse(info.value);
        }
        return SERVICE_INFO;
    } catch (error) {
        writeLog(`获取客服信息失败: ${error.message}`);
        return SERVICE_INFO; // 出错时返回默认值
    }
}

// 修改更新客服信息的接口
app.post('/api/admin/update-service-info', async (req, res) => {
    const { serviceInfo } = req.body;
    try {
        // 更新内存中的客服信息
        SERVICE_INFO = { ...SERVICE_INFO, ...serviceInfo };
        
        // 保存到数据库
        await new Promise((resolve, reject) => {
            db.run(
                'INSERT OR REPLACE INTO configs (key, value) VALUES (?, ?)',
                ['service_info', JSON.stringify(SERVICE_INFO)],
                (err) => {
                    if (err) reject(err);
                    else resolve();
                }
            );
        });

        writeLog('客服信息已更新并保存到数据库:');
        console.log(SERVICE_INFO);

        res.json({
            success: true,
            message: '客服信息已更新',
            serviceInfo: SERVICE_INFO
        });
    } catch (error) {
        writeLog(`更新客服信息失败: ${error.message}`);
        res.status(500).json({
            success: false,
            message: '更新客服信息失败: ' + error.message
        });
    }
});

// 修改获取客服信息的接口
app.get('/api/service-info', async (req, res) => {
    try {
        const info = await new Promise((resolve, reject) => {
            db.get('SELECT value FROM configs WHERE key = ?', ['service_info'], (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });

        if (info && info.value) {
            const serviceInfo = JSON.parse(info.value);
            res.json({
                success: true,
                serviceInfo: serviceInfo
            });
        } else {
            res.json({
                success: true,
                serviceInfo: SERVICE_INFO
            });
        }
    } catch (error) {
        writeLog(`获取客服信息失败: ${error.message}`);
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// 定义订阅计划
const SUBSCRIPTION_PLANS = {
    'week': {
        name: '周卡',
        days: 7
    },
    'biweek': {
        name: '双周卡',
        days: 15
    },
    'month': {
        name: '月卡',
        days: 30
    },
    'quarter': {
        name: '季卡',
        days: 90
    },
    'halfyear': {
        name: '半年卡',
        days: 180
    },
    'year': {
        name: '年卡',
        days: 365
    }
};

// 定义设备状态处理器
const DeviceStateHandlers = {
    // 永久激活状态
    ACTIVATED: {
        check: (device) => device.is_activated === 1,
        getResponse: (device) => ({
            success: true,
            deviceId: device.device_id,
            status: 'ACTIVATED',
            isActivated: true,
            serviceInfo: SERVICE_INFO
        })
    },

    // 订阅状态
    SUBSCRIBED: {
        check: (device) => {
            const now = Date.now();
            return device.expiry_time && device.expiry_time > now;
        },
        getResponse: (device) => {
            const now = Date.now();
            const remainingTime = device.expiry_time - now;
            return {
                success: true,
                deviceId: device.device_id,
                status: `SUBSCRIBED_${device.subscription_type?.toUpperCase() || ''}`,
                expiryTime: device.expiry_time,
                remainingDays: Math.ceil(remainingTime / (24 * 60 * 60 * 1000)),
                subscriptionType: device.subscription_type,
                serviceInfo: SERVICE_INFO
            };
        }
    },

    // 试用期状态
    TRIAL: {
        check: (device) => {
            const now = Date.now();
            const trialPeriod = 7 * 24 * 60 * 60 * 1000;
            return device.register_time + trialPeriod > now;
        },
        getResponse: (device) => {
            const now = Date.now();
            const trialPeriod = 7 * 24 * 60 * 60 * 1000;
            const remainingTime = (device.register_time + trialPeriod) - now;
            return {
                success: true,
                deviceId: device.device_id,
                status: 'TRIAL',
                expiryTime: device.register_time + trialPeriod,
                remainingDays: Math.ceil(remainingTime / (24 * 60 * 60 * 1000)),
                serviceInfo: SERVICE_INFO
            };
        }
    },

    // 过期状态
    EXPIRED: {
        check: (device) => true, // 默认状态
        getResponse: (device) => ({
            success: false,
            deviceId: device.device_id,
            status: 'EXPIRED',
            message: '设备已过期',
            serviceInfo: SERVICE_INFO
        })
    }
};

// 获取设备当前状态
function getDeviceState(device) {
    return Object.entries(DeviceStateHandlers).find(
        ([_, handler]) => handler.check(device)
    )[0];
}

// 获取设备状态响应
function getDeviceResponse(device) {
    const state = getDeviceState(device);
    return DeviceStateHandlers[state].getResponse(device);
}

// 设备状态检查接口
app.post('/api', async (req, res) => {
    try {
        const { deviceId, action, deviceInfo, timestamp } = req.body;
        
        if (!deviceId || !action) {
            return res.status(400).json({
                success: false,
                message: '缺少必要参数'
            });
        }

        if (action !== 'check-expiry') {
            return res.status(400).json({
                success: false,
                message: '无效的操作类型'
            });
        }

        const serverTime = Date.now();
        
        // 查找设备
        let device = await dbGet('SELECT * FROM devices WHERE device_id = ?', [deviceId]);
        
        // 如果设备不存在，创建新设备记录
        if (!device) {
            const trialExpiryTime = serverTime + (7 * 24 * 60 * 60 * 1000); // 7天试用期
            await dbRun(
                'INSERT INTO devices (device_id, device_info, register_time, expiry_time, last_request_time, status) VALUES (?, ?, ?, ?, ?, ?)',
                [deviceId, deviceInfo, serverTime, trialExpiryTime, serverTime, 'TRIAL']
            );
            device = await dbGet('SELECT * FROM devices WHERE device_id = ?', [deviceId]);
        }

        // 更新最后请求时间
        await dbRun(
            'UPDATE devices SET last_request_time = ?, device_info = ? WHERE device_id = ?',
            [serverTime, deviceInfo, deviceId]
        );

        // 获取客服信息
        const serviceInfo = await getServiceInfo();

        // 确定设备状态
        const isExpired = device.expiry_time <= serverTime && !device.is_activated;
        const status = device.is_activated ? 'ACTIVATED' : 
                      isExpired ? 'EXPIRED' : 
                      device.status || 'TRIAL';

        // 计算剩余时间描述
        const remainingTime = Math.max(0, device.expiry_time - serverTime);
        const days = Math.floor(remainingTime / (24 * 60 * 60 * 1000));
        const hours = Math.floor((remainingTime % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
        const timeDescription = isExpired ? "已过期" : `${days}天${hours}小时`;

        res.json({
            success: true,
            deviceId: device.device_id,
            status: status,
            serverTime: serverTime,
            expiryTime: device.expiry_time,
            isActivated: device.is_activated === 1,
            timeDescription: timeDescription,
            remainingTime: remainingTime,
            serviceInfo: serviceInfo
        });

    } catch (error) {
        writeLog(`检查设备状态失败: ${error.message}`);
        res.status(500).json({
            success: false,
            message: 'Server error'
        });
    }
});

// 获取设备列表
app.get('/api/devices', async (req, res) => {
    try {
        // 获取所有设备
        const devices = await dbAll('SELECT * FROM devices ORDER BY register_time DESC');
        
        // 处理每个设备的状态
        const deviceList = await Promise.all(devices.map(async (device) => {
            const now = Date.now();
            const isExpired = device.expiry_time <= now;
            
            return {
                deviceId: device.device_id,
                deviceInfo: device.device_info || '',
                registerTime: device.register_time,
                expiryTime: device.expiry_time,
                lastRequestTime: device.last_request_time,
                status: isExpired ? 'EXPIRED' : 'TRIAL',
                remainingTime: isExpired ? 0 : device.expiry_time - now
            };
        }));

        res.json({
            success: true,
            devices: deviceList
        });
    } catch (error) {
        writeLog(`获取设备列表失败: ${error.message}`);
        res.status(500).json({
            success: false,
            message: '获取设备列表失败',
            error: error.message
        });
    }
});

// 添加设备状态检查接口
app.get('/api/device/:deviceId/status', async (req, res) => {
    try {
        const { deviceId } = req.params;
        const device = await dbGet('SELECT * FROM devices WHERE device_id = ?', [deviceId]);
        
        if (!device) {
            return res.status(404).json({
                success: false,
                message: '设备不存在'
            });
        }

        const now = Date.now();
        const isExpired = device.expiry_time <= now;
        
        res.json({
            success: true,
            deviceId: device.device_id,
            status: isExpired ? 'EXPIRED' : 'TRIAL',
            expiryTime: device.expiry_time,
            remainingTime: isExpired ? 0 : device.expiry_time - now
        });
    } catch (error) {
        writeLog(`获取设备状态失败: ${error.message}`);
        res.status(500).json({
            success: false,
            message: '获取设备状态失败'
        });
    }
});

// 删除设备
app.post('/api/admin/delete-device', async (req, res) => {
    const { deviceId } = req.body;
    try {
        await dbRun('DELETE FROM devices WHERE device_id = ?', [deviceId]);
        res.json({
            success: true,
            message: '设备已删除'
        });
    } catch (error) {
        writeLog(`删除设备失败: ${error.message}`);
        res.status(500).json({
            success: false,
            message: '删除设备失败'
        });
    }
});

// 更新到期时间
app.post('/api/admin/update-expiry', async (req, res) => {
    const { deviceId, duration } = req.body;
    try {
        const device = await dbGet('SELECT * FROM devices WHERE device_id = ?', [deviceId]);
        if (!device) {
            return res.status(404).json({
                success: false,
                message: '设备不存在'
            });
        }

        const now = Date.now();
        const newExpiryTime = Math.max(now, device.expiry_time) + duration;
        
        await dbRun(
            'UPDATE devices SET expiry_time = ? WHERE device_id = ?',
            [newExpiryTime, deviceId]
        );

        res.json({
            success: true,
            message: '到期时间已更新',
            expiryTime: newExpiryTime
        });
    } catch (error) {
        writeLog(`更新到期时间失败: ${error.message}`);
        res.status(500).json({
            success: false,
            message: '更新到期时间失败'
        });
    }
});

// 订阅接口
app.post('/api/admin/subscribe', async (req, res) => {
    const { deviceId, planType } = req.body;
    
    try {
        const device = await dbGet('SELECT * FROM devices WHERE device_id = ?', [deviceId]);

        if (!device) {
            return res.status(404).json({
                success: false,
                message: '设备不存在'
            });
        }

        const plan = SUBSCRIPTION_PLANS[planType.toLowerCase()];
        if (!plan) {
            return res.status(400).json({
                success: false,
                message: '无效的订阅类型'
            });
        }

        const now = Date.now();
        const duration = plan.days * 24 * 60 * 60 * 1000;
        
        // 计算新的到期时间：
        // 1. 如果当前有未过期的订阅，在原有时间基础上增加
        // 2. 如果已过期或在试用期，从当前时间开始计算
        const newExpiryTime = Math.max(now, device.expiry_time || now) + duration;

        // 更新设备状态为订阅状态
        await dbRun(
            'UPDATE devices SET status = ?, expiry_time = ?, subscription_type = ? WHERE device_id = ?',
            ['SUBSCRIBED', newExpiryTime, planType, deviceId]
        );

        // 获取更新后的设备状态
        const updatedDevice = await dbGet('SELECT * FROM devices WHERE device_id = ?', [deviceId]);
        const remainingTime = newExpiryTime - now;

        res.json({
            success: true,
            message: `订阅成功: ${plan.name}`,
            deviceId: deviceId,
            planType: planType,
            planName: plan.name,
            expiryTime: newExpiryTime,
            remainingDays: Math.ceil(remainingTime / (24 * 60 * 60 * 1000)),
            status: 'SUBSCRIBED'
        });

    } catch (error) {
        writeLog(`订阅失败: ${error.message}`);
        res.status(500).json({
            success: false,
            message: '订阅失败'
        });
    }
});

// 获取订阅计划列表
app.get('/api/subscription-plans', (req, res) => {
    try {
        const plans = Object.entries(SUBSCRIPTION_PLANS).map(([type, plan]) => ({
            type,
            name: plan.name,
            days: plan.days,
            description: `${plan.name} (${plan.days}天)`
        }));

        res.json({
            success: true,
            plans,
            serviceInfo: SERVICE_INFO
        });
    } catch (error) {
        writeLog(`获取订阅计划失败: ${error.message}`);
        res.status(500).json({
            success: false,
            message: '获取订阅计划失败'
        });
    }
});

// 修改设备信息接口
app.post('/api/admin/update-device', async (req, res) => {
    const { deviceId, deviceName, expiryTime } = req.body;
    writeLog(`收到更新设备请求: ${JSON.stringify({
        deviceId,
        deviceName,
        expiryTime,
        currentTime: Date.now()
    })}`);

    try {
        const device = await dbGet('SELECT * FROM devices WHERE device_id = ?', [deviceId]);
        writeLog('当前设备信息:');
        console.log(device);

        if (!device) {
            return res.status(404).json({
                success: false,
                message: '设备不存在'
            });
        }

        const updates = [];
        const params = [];

        if (deviceName !== undefined) {
            updates.push('device_name = ?');
            params.push(deviceName);
        }

        if (expiryTime !== undefined) {
            writeLog(`更新到期时间: ${JSON.stringify({
                oldExpiryTime: device.expiry_time,
                newExpiryTime: expiryTime,
                difference: expiryTime - device.expiry_time
            })}`);
            updates.push('expiry_time = ?');
            params.push(expiryTime);
        }

        if (updates.length === 0) {
            return res.json({
                success: true,
                message: '没有需要更新的内容'
            });
        }

        params.push(deviceId);
        await dbRun(
            `UPDATE devices SET ${updates.join(', ')} WHERE device_id = ?`,
            params
        );

        // 获取更新后的设备信息
        const updatedDevice = await dbGet('SELECT * FROM devices WHERE device_id = ?', [deviceId]);
        writeLog('更新后的设备信息:');
        console.log(updatedDevice);
        writeLog(`设备状态: ${getDeviceStatus(updatedDevice)}`);

        res.json({
            success: true,
            message: '设备信息已更新',
            device: {
                ...updatedDevice,
                registerTime: formatDateTime(updatedDevice.register_time),
                expiryTime: formatDateTime(updatedDevice.expiry_time),
                status: getDeviceStatus(updatedDevice)
            }
        });

    } catch (error) {
        writeLog(`更新设备信息失败: ${error.message}`);
        res.status(500).json({
            success: false,
            message: '更新设备信息失败'
        });
    }
});

// 工具函数
function formatDateTime(timestamp) {
    return new Date(timestamp).toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai'
    });
}

// 数据库操作包装函数
function dbRun(sql, params = []) {
    return dbOperation(db => {
        return new Promise((resolve, reject) => {
            db.run(sql, params, function(err) {
                if (err) reject(err);
                else resolve(this);
            });
        });
    });
}

function dbGet(sql, params = []) {
    return dbOperation(db => {
        return new Promise((resolve, reject) => {
            db.get(sql, params, (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });
    });
}

function dbAll(sql, params = []) {
    return dbOperation(db => {
        return new Promise((resolve, reject) => {
            db.all(sql, params, (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });
    });
}

// 修改错误处理中间件
const errorHandler = (err, req, res, next) => {
    const errorLog = {
        message: err.message,
        path: req.path,
        method: req.method,
        ip: req.ip,
        timestamp: new Date().toLocaleString('zh-CN')
    };
    
    writeLog(`错误: ${JSON.stringify(errorLog)}`);

    res.status(err.status || 500).json({
        success: false,
        message: process.env.NODE_ENV === 'production' 
            ? '服务器错误' 
            : err.message
    });
};

app.use(errorHandler);

// 异步错误处理包装器
const asyncHandler = (fn) => (req, res, next) =>
    Promise.resolve(fn(req, res, next)).catch(next);

// 使用示例
app.post('/api/activate', asyncHandler(async (req, res) => {
    // 原有激活逻辑...
}));

// 初始化metrics
const collectDefaultMetrics = prometheus.collectDefaultMetrics;
collectDefaultMetrics({ timeout: 5000 });

// 请求计数器
const httpRequestDuration = new prometheus.Histogram({
    name: 'http_request_duration_seconds',
    help: 'Duration of HTTP requests in seconds',
    labelNames: ['method', 'route', 'status_code'],
    buckets: [0.1, 0.5, 1, 2, 5]
});

// 监控中间件
app.use((req, res, next) => {
    const start = Date.now();
    res.on('finish', () => {
        const duration = Date.now() - start;
        httpRequestDuration.observe(
            {
                method: req.method,
                route: req.route?.path || req.path,
                status_code: res.statusCode
            },
            duration / 1000
        );
    });
    next();
});

// 暴露metrics端点
app.get('/metrics', async (req, res) => {
    res.set('Content-Type', prometheus.register.contentType);
    res.end(await prometheus.register.metrics());
});

// 添加全局错误处理
process.on('unhandledRejection', (reason, promise) => {
    writeLog(`未处理的 Promise 拒绝: ${reason}`);
});

process.on('uncaughtException', (error) => {
    writeLog(`未捕获的异常: ${error.message}`);
});

// 优雅关闭处理
const shutdown = async () => {
    writeLog('正在关闭服务器...');
    
    if (server) {
        await new Promise(resolve => server.close(resolve));
    }
    
    // 关闭所有数据库连接
    for (const connection of dbPool._pool) {
        await dbPool.destroy(connection);
    }
    
    // 关闭主数据库连接
    await new Promise((resolve) => {
        db.close(() => {
            writeLog('数据库连接已关闭');
            resolve();
        });
    });
    
    writeLog('服务器已关闭');
    process.exit(0);
};

process.on('SIGTERM', shutdown);
process.on('SIGINT', shutdown);

// 修改服务器启动代码
let server;

async function startServer() {
    try {
        writeLog('开始启动服务器...');

        // 确保表存在
        db.serialize(() => {
            // 创建设备表
            db.run(`
                CREATE TABLE IF NOT EXISTS devices (
                    device_id TEXT PRIMARY KEY,
                    device_name TEXT,
                    device_info TEXT,
                    register_time INTEGER,
                    expiry_time INTEGER,
                    is_activated INTEGER DEFAULT 0,
                    last_request_time INTEGER,
                    subscription_type TEXT,
                    device_signature TEXT,
                    hardware_info TEXT,
                    last_verify_time INTEGER,
                    status TEXT DEFAULT 'TRIAL'
                )
            `);

            // 创建配置表
            db.run(`
                CREATE TABLE IF NOT EXISTS configs (
                    key TEXT PRIMARY KEY,
                    value TEXT
                )
            `);

            // 创建日志表
            db.run(`
                CREATE TABLE IF NOT EXISTS request_logs (
                    device_id TEXT PRIMARY KEY,
                    request_time INTEGER,
                    request_type TEXT,
                    request_path TEXT
                )
            `);
        });

        // 启动服务器
        server = app.listen(port, async () => {
            writeLog(`服务器运行在 http://localhost:${port}`);
            await loadServiceInfo();
            writeLog('服务器启动完成');
        });
    } catch (error) {
        writeLog(`启动服务器失败: ${error.message}`);
        process.exit(1);
    }
}

// 启动服务器
startServer();

// 添加重置数据库接口
app.post('/api/admin/reset-database', async (req, res) => {
    try {
        writeLog('收到重置数据库请求');
        
        // 1. 重置数据库
        writeLog('开始重置数据库...');
        await dropAllTables(db);
        writeLog('数据库表重置完成');

        // 2. 重新加载客服信息
        await loadServiceInfo();
        writeLog('客服信息重新加载完成');

        // 3. 返回成功响应
        res.json({
            success: true,
            message: '数据库已重置',
            timestamp: new Date().toLocaleString('zh-CN', {
                timeZone: 'Asia/Shanghai'
            })
        });

    } catch (error) {
        writeLog(`重置数据库失败: ${error.message}`);
        res.status(500).json({
            success: false,
            message: '重置数据库失败: ' + error.message
        });
    }
});

// 添加删除日志的接口
app.post('/api/admin/clear-logs', async (req, res) => {
    const { type, before, deviceId } = req.body;
    
    try {
        let deleteQuery;
        let params = [];
        const now = Date.now();

        switch (type) {
            case 'all': // 删除所有日志
                deleteQuery = 'DELETE FROM request_logs';
                break;

            case 'date': // 删除指定日期之前的日志
                if (!before) {
                    return res.status(400).json({
                        success: false,
                        message: '需要指定日期'
                    });
                }
                deleteQuery = 'DELETE FROM request_logs WHERE request_time < ?';
                params = [new Date(before).getTime()];
                break;

            case 'device': // 删除指定设备的日志
                if (!deviceId) {
                    return res.status(400).json({
                        success: false,
                        message: '需要指定设备ID'
                    });
                }
                deleteQuery = 'DELETE FROM request_logs WHERE device_id = ?';
                params = [deviceId];
                break;

            case 'expired': // 删除过期设备的日志
                deleteQuery = `
                    DELETE FROM request_logs 
                    WHERE device_id IN (
                        SELECT device_id 
                        FROM devices 
                        WHERE expiry_time < ? AND is_activated = 0
                    )`;
                params = [now];
                break;

            default:
                return res.status(400).json({
                    success: false,
                    message: '无效的删除类型'
                });
        }

        // 执行删除操作
        const result = await dbRun(deleteQuery, params);
        
        // 执行 VACUUM 优化空间
        await dbRun('VACUUM');

        writeLog(`日志清理完成 - 类型: ${type}, 影响行数: ${result.changes}`);

        res.json({
            success: true,
            message: '日志清理完成',
            type,
            affectedRows: result.changes,
            timestamp: new Date().toLocaleString('zh-CN', {
                timeZone: 'Asia/Shanghai'
            })
        });

    } catch (error) {
        writeLog(`清理日志失败: ${error.message}`);
        res.status(500).json({
            success: false,
            message: '清理日志失败: ' + error.message
        });
    }
});

// 添加获取日志统计信息的接口
app.get('/api/admin/log-stats', async (req, res) => {
    try {
        const stats = await dbGet(`
            SELECT 
                COUNT(*) as total_logs,
                COUNT(DISTINCT device_id) as unique_devices,
                MIN(request_time) as earliest_log,
                MAX(request_time) as latest_log,
                (SELECT COUNT(*) FROM request_logs WHERE request_time > ?) as recent_logs
            FROM request_logs
        `, [Date.now() - 24 * 60 * 60 * 1000]); // 最近24小时的日志数

        // 获取日志占用空间
        const spaceStats = await dbGet('SELECT page_count * page_size as size FROM pragma_page_count, pragma_page_size');

        res.json({
            success: true,
            stats: {
                totalLogs: stats.total_logs,
                uniqueDevices: stats.unique_devices,
                earliestLog: new Date(stats.earliest_log).toLocaleString('zh-CN'),
                latestLog: new Date(stats.latest_log).toLocaleString('zh-CN'),
                recentLogs: stats.recent_logs,
                dbSize: Math.round(spaceStats.size / 1024 / 1024 * 100) / 100 + 'MB'
            }
        });

    } catch (error) {
        writeLog(`获取日志统计失败: ${error.message}`);
        res.status(500).json({
            success: false,
            message: '获取日志统计失败'
        });
    }
});

// 管理员 API 路由组
const adminRouter = express.Router();

// 获取设备列表（带详细信息）
adminRouter.get('/devices', async (req, res) => {
    try {
        const { showArchived } = req.query;
        
        let query = `
            SELECT 
                d.*,
                (
                    SELECT MAX(request_time) 
                    FROM request_logs rl 
                    WHERE rl.device_id = d.device_id
                ) as last_request_time
            FROM devices d
            WHERE 1=1
        `;
        
        // 默认只显示未归档设备
        if (!showArchived) {
            query += ` AND (json_extract(device_info, '$.archived') IS NULL OR json_extract(device_info, '$.archived') = 0)`;
        }

        const devices = await dbAll(query);
        const now = Date.now();
        
        const formattedDevices = devices.map(device => {
            const deviceInfo = typeof device.device_info === 'string' ? 
                JSON.parse(device.device_info) : device.device_info;
            
            const remainingTime = device.expiry_time > now ? device.expiry_time - now : 0;
            return {
                deviceId: device.device_id,
                deviceInfo: deviceInfo,
                registerTime: device.register_time,
                expiryTime: device.expiry_time,
                lastRequestTime: device.last_request_time,
                status: device.expiry_time > now ? 'TRIAL' : 'EXPIRED',
                remainingDays: Math.ceil(remainingTime / (24 * 60 * 60 * 1000)),
                isArchived: deviceInfo?.archived || false,
                archiveTime: deviceInfo?.archive_time,
                archiveReason: deviceInfo?.archive_reason
            };
        });

        res.json({
            success: true,
            devices: formattedDevices
        });
    } catch (error) {
        writeLog(`获取设备列表失败: ${error.message}`);
        res.status(500).json({
            success: false,
            message: '获取设备列表失败'
        });
    }
});

// 归档设备
adminRouter.post('/archive-device', async (req, res) => {
    const { deviceId, reason } = req.body;
    try {
        const device = await dbGet('SELECT * FROM devices WHERE device_id = ?', [deviceId]);
        if (!device) {
            return res.status(404).json({
                success: false,
                message: '设备不存在'
            });
        }

        const deviceInfo = {
            ...(typeof device.device_info === 'string' ? JSON.parse(device.device_info) : device.device_info),
            archived: true,
            archive_time: Date.now(),
            archive_reason: reason
        };

        await dbRun(
            'UPDATE devices SET device_info = ? WHERE device_id = ?',
            [JSON.stringify(deviceInfo), deviceId]
        );

        res.json({
            success: true,
            message: '设备已归档'
        });
    } catch (error) {
        writeLog(`归档设备失败: ${error.message}`);
        res.status(500).json({
            success: false,
            message: '归档设备失败'
        });
    }
});

// 取消归档设备
adminRouter.post('/unarchive-device', async (req, res) => {
    const { deviceId } = req.body;
    try {
        const device = await dbGet('SELECT * FROM devices WHERE device_id = ?', [deviceId]);
        if (!device) {
            return res.status(404).json({
                success: false,
                message: '设备不存在'
            });
        }

        const deviceInfo = {
            ...(typeof device.device_info === 'string' ? JSON.parse(device.device_info) : device.device_info),
            archived: false,
            archive_time: null,
            archive_reason: null
        };

        await dbRun(
            'UPDATE devices SET device_info = ? WHERE device_id = ?',
            [JSON.stringify(deviceInfo), deviceId]
        );

        res.json({
            success: true,
            message: '已取消归档'
        });
    } catch (error) {
        writeLog(`取消归档失败: ${error.message}`);
        res.status(500).json({
            success: false,
            message: '取消归档失败'
        });
    }
});

// 批量更新到期时间
adminRouter.post('/batch-update-expiry', async (req, res) => {
    const { deviceIds, duration } = req.body;
    try {
        const now = Date.now();
        const results = await Promise.all(deviceIds.map(async deviceId => {
            try {
                const device = await dbGet('SELECT * FROM devices WHERE device_id = ?', [deviceId]);
                if (!device) return { deviceId, success: false, message: '设备不存在' };

                const newExpiryTime = Math.max(now, device.expiry_time) + duration;
                await dbRun(
                    'UPDATE devices SET expiry_time = ? WHERE device_id = ?',
                    [newExpiryTime, deviceId]
                );

                return { deviceId, success: true, expiryTime: newExpiryTime };
            } catch (error) {
                return { deviceId, success: false, message: error.message };
            }
        }));

        res.json({
            success: true,
            results
        });
    } catch (error) {
        writeLog(`批量更新到期时间失败: ${error.message}`);
        res.status(500).json({
            success: false,
            message: '批量更新到期时间失败'
        });
    }
});

// 使用管理员路由
app.use('/api/admin', adminRouter);
