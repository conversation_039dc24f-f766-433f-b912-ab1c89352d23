# Gradle 性能优化
org.gradle.jvmargs=-Xmx2048m -XX:+UseParallelGC -Dfile.encoding=UTF-8
org.gradle.parallel=true
org.gradle.daemon=true
org.gradle.caching=true
org.gradle.configureondemand=true

# Android 配置
android.useAndroidX=true
android.enableJetifier=true
android.nonTransitiveRClass=true

# Kotlin 编译优化
kotlin.incremental=true
kotlin.incremental.java=true
kotlin.caching.enabled=true
kotlin.parallel.tasks.in.project=true

# 编译优化
kapt.incremental.apt=true
kapt.use.worker.api=true
android.lifecycleProcessor.incremental=true

# R8 优化
android.enableR8.fullMode=true
android.enableResourceOptimizations=true

# 构建缓存配置
org.gradle.unsafe.configuration-cache=true