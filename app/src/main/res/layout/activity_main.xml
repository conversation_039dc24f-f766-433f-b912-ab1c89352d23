<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <EditText
        android:id="@+id/inputEditText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="请输入数据"
        android:minHeight="100dp"
        android:gravity="top"
        android:padding="8dp"
        android:inputType="textMultiLine"
        android:background="@android:drawable/edit_text" />

    <Button
        android:id="@+id/reparseButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="重新解析" />

    <EditText
        android:id="@+id/outputEditText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:hint="输出结果"
        android:minHeight="100dp"
        android:gravity="top"
        android:padding="8dp"
        android:inputType="textMultiLine"
        android:background="@android:drawable/edit_text" />

    <Button
        android:id="@+id/saveButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="解析并录入数据库" />

</LinearLayout> 