<?xml version="1.0" encoding="utf-8"?>
<shortcuts xmlns:android="http://schemas.android.com/apk/res/android">
    <shortcut
        android:shortcutId="management"
        android:enabled="true"
        android:icon="@drawable/ic_management"
        android:shortcutShortLabel="@string/shortcut_management_short"
        android:shortcutLongLabel="@string/shortcut_management_long">
        <intent
            android:action="android.intent.action.VIEW"
            android:targetPackage="com.example.myshujuguanli"
            android:targetClass="com.example.myshujuguanli.MainActivity">
            <extra android:name="screen" android:value="management" />
        </intent>
    </shortcut>
    
    <shortcut
        android:shortcutId="datamanage"
        android:enabled="true"
        android:icon="@drawable/ic_datamanage"
        android:shortcutShortLabel="@string/shortcut_datamanage_short"
        android:shortcutLongLabel="@string/shortcut_datamanage_long">
        <intent
            android:action="android.intent.action.VIEW"
            android:targetPackage="com.example.myshujuguanli"
            android:targetClass="com.example.myshujuguanli.MainActivity">
            <extra android:name="screen" android:value="datamanage" />
        </intent>
    </shortcut>
</shortcuts> 