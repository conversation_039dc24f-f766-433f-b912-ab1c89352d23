package com.example.myshujuguanli.theme

import android.content.Context
import androidx.compose.material3.ColorScheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.graphics.Color
import kotlin.random.Random

object ThemeManager {
    private const val PREFS_NAME = "theme_prefs"
    private const val KEY_THEME_INDEX = "theme_index"
    private const val KEY_IS_RANDOM_THEME = "is_random_theme"
    
    // 添加缓存随机主题的变量，避免每次都随机生成新主题
    private var cachedRandomThemeIndex: Int? = null
    
    // 预定义主题集合
    val themes = listOf(
        ThemeDefinition(
            name = "蓝色主题",
            lightScheme = lightColorScheme(
                primary = Color(0xFF1976D2),
                primaryContainer = Color(0xFFBBDEFB)
            ),
            darkScheme = darkColorScheme(
                primary = Color(0xFF82B1FF),
                primaryContainer = Color(0xFF0D47A1),
                surface = Color(0xFF1A1A1A),
                onSurface = Color(0xFFE1E1E1)
            )
        ),
        ThemeDefinition(
            name = "绿色主题",
            lightScheme = lightColorScheme(
                primary = Color(0xFF388E3C),
                primaryContainer = Color(0xFFC8E6C9)
            ),
            darkScheme = darkColorScheme(
                primary = Color(0xFF81C784),
                primaryContainer = Color(0xFF1B5E20),
                surface = Color(0xFF1A1A1A),
                onSurface = Color(0xFFE1E1E1)
            )
        ),
        ThemeDefinition(
            name = "青色主题",
            lightScheme = lightColorScheme(
                primary = Color(0xFF572FA2),
                primaryContainer = Color(0xFF8758D9)
            ),
            darkScheme = darkColorScheme(
                primary = Color(0xFF04A986),
                primaryContainer = Color(0xFF30B48E),
                surface = Color(0xFF1A1A1A),
                onSurface = Color(0xFFE1E1E1)
            )
        ),
        ThemeDefinition(
            name = "紫色主题",
            lightScheme = lightColorScheme(
                primary = Color(0xFF7B1FA2),
                primaryContainer = Color(0xFFE1BEE7)
            ),
            darkScheme = darkColorScheme(
                primary = Color(0xFF942CA6),
                primaryContainer = Color(0xFF4A148C),
                surface = Color(0xFF1A1A1A),
                onSurface = Color(0xFFE1E1E1)
            )
        ),
        ThemeDefinition(
            name = "灰色主题",
            lightScheme = lightColorScheme(
                primary = Color(0xFF546E7A),
                primaryContainer = Color(0xFFCFD8DC)
            ),
            darkScheme = darkColorScheme(
                primary = Color(0xBF1AD568),
                primaryContainer = Color(0xFF07F569),
                surface = Color(0xFF1A1A1A),
                onSurface = Color(0xFFE1E1E1)
            )
        ),
        ThemeDefinition(
            name = "橙色主题",
            lightScheme = lightColorScheme(
                primary = Color(0xFFF57C00),
                primaryContainer = Color(0xFFFFE0B2)
            ),
            darkScheme = darkColorScheme(
                primary = Color(0xFFF57C00),
                primaryContainer = Color(0xFFE65100),
                surface = Color(0xFF1A1A1A),
                onSurface = Color(0xFFE1E1E1)
            )
        )
    )
    
    // 从存储加载主题设置
    fun loadThemeSettings(context: Context, isDarkMode: Boolean): ColorScheme {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val isRandomTheme = prefs.getBoolean(KEY_IS_RANDOM_THEME, false)
        val savedThemeIndex = prefs.getInt(KEY_THEME_INDEX, 0)
        
        return if (isRandomTheme) {
            // 检查是否已经有缓存的随机主题索引
            val themeIndex = cachedRandomThemeIndex ?: run {
                // 如果没有，生成新的随机主题索引并缓存
                val newIndex = Random.nextInt(themes.size)
                cachedRandomThemeIndex = newIndex
                // 将随机生成的主题保存，以便在应用重启时恢复同一个主题
                prefs.edit().putInt("random_theme_index", newIndex).apply()
                newIndex
            }
            
            if (isDarkMode) themes[themeIndex].darkScheme else themes[themeIndex].lightScheme
        } else {
            // 非随机主题模式，清除缓存的随机主题
            cachedRandomThemeIndex = null
            val index = if (savedThemeIndex in themes.indices) savedThemeIndex else 0
            if (isDarkMode) themes[index].darkScheme else themes[index].lightScheme
        }
    }
    
    // 保存主题设置
    fun saveThemeSettings(context: Context, themeIndex: Int) {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            .edit()
            .putInt(KEY_THEME_INDEX, themeIndex)
            .putBoolean(KEY_IS_RANDOM_THEME, false)
            .apply()
        // 清除缓存的随机主题
        cachedRandomThemeIndex = null
    }
    
    // 启用随机主题
    fun enableRandomTheme(context: Context) {
        // 启用随机主题时，生成一个新的随机主题并缓存
        val randomIndex = Random.nextInt(themes.size)
        cachedRandomThemeIndex = randomIndex
        
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            .edit()
            .putBoolean(KEY_IS_RANDOM_THEME, true)
            .putInt("random_theme_index", randomIndex) // 保存随机主题索引
            .remove(KEY_THEME_INDEX)
            .apply()
    }
    
    // 获取指定主题的ColorScheme
    fun getThemeScheme(themeIndex: Int, isDarkMode: Boolean): ColorScheme? {
        return if (themeIndex in themes.indices) {
            if (isDarkMode) themes[themeIndex].darkScheme else themes[themeIndex].lightScheme
        } else {
            null
        }
    }
    
    // 获取随机主题 - 修改为使用缓存的随机索引或创建新的
    fun getRandomTheme(isDarkMode: Boolean): ColorScheme {
        val randomIndex = cachedRandomThemeIndex ?: Random.nextInt(themes.size).also { 
            cachedRandomThemeIndex = it 
        }
        return if (isDarkMode) themes[randomIndex].darkScheme else themes[randomIndex].lightScheme
    }
    
    // 清除随机主题缓存，用于应用冷启动时
    fun clearRandomThemeCache() {
        cachedRandomThemeIndex = null
    }
    
    // 获取当前主题信息
    fun getCurrentThemeInfo(context: Context): ThemeInfo {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val isRandomTheme = prefs.getBoolean(KEY_IS_RANDOM_THEME, false)
        val themeIndex = if (isRandomTheme) {
            // 如果是随机主题，使用缓存的随机索引或从存储中恢复
            cachedRandomThemeIndex ?: prefs.getInt("random_theme_index", 0)
        } else {
            prefs.getInt(KEY_THEME_INDEX, 0)
        }
        return ThemeInfo(isRandomTheme, themeIndex)
    }
}

// 主题定义数据类
data class ThemeDefinition(
    val name: String,
    val lightScheme: ColorScheme,
    val darkScheme: ColorScheme
)

// 主题信息数据类
data class ThemeInfo(
    val isRandomTheme: Boolean,
    val themeIndex: Int
)

// 提供CompositionLocal以在Compose中访问主题
val LocalAppTheme = staticCompositionLocalOf { ThemeInfo(false, 0) } 