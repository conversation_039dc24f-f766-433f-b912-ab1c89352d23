package com.example.myshujuguanli.viewmodels

import android.content.Context
import androidx.compose.material3.ColorScheme
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import com.example.myshujuguanli.theme.ThemeInfo
import com.example.myshujuguanli.theme.ThemeManager

class ThemeViewModel : ViewModel() {
    // 当前颜色方案
    private val _currentColorScheme = mutableStateOf<ColorScheme?>(null)
    val currentColorScheme: State<ColorScheme?> = _currentColorScheme
    
    // 主题信息包含主题索引和是否为随机主题
    private val _themeInfo = mutableStateOf(ThemeInfo(false, 0))
    val themeInfo: State<ThemeInfo> = _themeInfo
    
    // 当前主题索引的便捷获取方法
    val currentThemeIndex: Int
        get() = _themeInfo.value.themeIndex
    
    // 是否是随机主题的便捷获取方法
    val isRandomTheme: Boolean
        get() = _themeInfo.value.isRandomTheme
    
    // 加载主题
    fun loadTheme(context: Context, isDarkMode: Boolean) {
        _currentColorScheme.value = ThemeManager.loadThemeSettings(context, isDarkMode)
        _themeInfo.value = ThemeManager.getCurrentThemeInfo(context)
    }
    
    // 更新主题
    fun updateTheme(context: Context, themeIndex: Int, isDarkMode: Boolean) {
        ThemeManager.saveThemeSettings(context, themeIndex)
        _themeInfo.value = ThemeManager.getCurrentThemeInfo(context)
        _currentColorScheme.value = ThemeManager.getThemeScheme(themeIndex, isDarkMode)
    }
    
    // 启用随机主题
    fun enableRandomTheme(context: Context, isDarkMode: Boolean) {
        ThemeManager.enableRandomTheme(context)
        _themeInfo.value = ThemeManager.getCurrentThemeInfo(context)
        _currentColorScheme.value = ThemeManager.getRandomTheme(isDarkMode)
    }
    
    // 更新当前颜色方案，保持兼容性
    fun updateThemeColor(colorScheme: ColorScheme) {
        _currentColorScheme.value = colorScheme
    }
    
    // 新增：仅更新主题信息不重新生成随机主题
    fun updateThemeInfoOnly(context: Context) {
        _themeInfo.value = ThemeManager.getCurrentThemeInfo(context)
    }
} 