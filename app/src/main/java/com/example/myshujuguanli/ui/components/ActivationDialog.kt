package com.example.myshujuguanli.ui.components

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.widget.Toast
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.ClickableText
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ContentCopy
import androidx.compose.material.icons.filled.Message
import androidx.compose.material.icons.filled.Phone
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.DialogProperties
import com.example.myshujuguanli.utils.SecurityUtils
import java.text.SimpleDateFormat
import java.util.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ripple.rememberRipple
import androidx.compose.runtime.remember
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.ui.draw.clip

@Composable
fun ActivationDialog(
    status: SecurityUtils.StatusInfo,
    onActivate: (String) -> Unit,
    onContinue: () -> Unit
) {
    if (status.status == SecurityUtils.AppStatus.ACTIVATED) {
        LaunchedEffect(Unit) {
            onContinue()
        }
        return
    }

    val context = LocalContext.current
    val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager

    fun copyToClipboard(text: String, label: String) {
        val clip = ClipData.newPlainText(label, text)
        clipboardManager.setPrimaryClip(clip)
        Toast.makeText(context, "${label}已复制", Toast.LENGTH_SHORT).show()
    }

    AlertDialog(
        onDismissRequest = { 
            if (status.expiryTime > status.serverTime) {
                onContinue()
            }
        },
        title = {
            Text("使用提示")
        },
        text = {
            Column(
                modifier = Modifier.padding(8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // 显示设备ID
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Start,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "设备ID：",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = status.deviceId,
                        style = MaterialTheme.typography.bodyMedium.copy(
                            color = MaterialTheme.colorScheme.primary,
                            fontWeight = FontWeight.Bold
                        ),
                        modifier = Modifier
                            .clip(RoundedCornerShape(4.dp))
                            .clickable(
                                interactionSource = remember { MutableInteractionSource() },
                                indication = rememberRipple(bounded = true)
                            ) {
                                copyToClipboard(status.deviceId, "设备ID")
                            }
                            .padding(horizontal = 8.dp, vertical = 4.dp)
                    )
                }

                // 显示使用期限（包括过期状态）
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = if (status.status == SecurityUtils.AppStatus.TRIAL) {
                            val remainingTime = status.expiryTime - status.serverTime
                            val days = remainingTime / (24 * 60 * 60 * 1000)
                            val hours = (remainingTime % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000)
                            "${days}天${hours}小时"
                        } else {
                            status.timeDescription
                        },
                        style = MaterialTheme.typography.headlineSmall,
                        color = if (status.expiryTime > status.serverTime) 
                            MaterialTheme.colorScheme.primary
                        else 
                            MaterialTheme.colorScheme.error
                    )
                    if (status.expiryTime > 0) {
                        Text(
                            text = "使用期限至：${formatDateTime(status.expiryTime)}",
                            style = MaterialTheme.typography.bodyMedium,
                            color = if (status.expiryTime > status.serverTime)
                                MaterialTheme.colorScheme.primary
                            else
                                MaterialTheme.colorScheme.error
                        )
                    }
                }

                // 显示所有客服信息
                with(status.serviceInfo) {
                    if (qq.isNotBlank()) {
                        ContactRow("QQ", qq) { text ->
                            copyToClipboard(text, "QQ号")
                        }
                    }
                    if (weixin.isNotBlank()) {
                        ContactRow("微信", weixin) { text ->
                            copyToClipboard(text, "微信号")
                        }
                    }
                    if (phone.isNotBlank()) {
                        ContactRow("电话", phone) { text ->
                            copyToClipboard(text, "电话号码")
                        }
                    }
                    if (description.isNotBlank()) {
                        Text(
                            text = description,
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.padding(vertical = 4.dp)
                        )
                    }
                }
            }
        },
        confirmButton = {
            Button(
                onClick = onContinue,
                colors = ButtonDefaults.buttonColors(
                    containerColor = if (status.expiryTime <= status.serverTime)
                        MaterialTheme.colorScheme.error
                    else
                        MaterialTheme.colorScheme.primary
                )
            ) {
                Text(
                    if (status.expiryTime <= status.serverTime)
                        "退出"
                    else
                        "继续"
                )
            }
        }
    )
}

private fun formatDateTime(timestamp: Long): String {
    return SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        .format(Date(timestamp))
}

private fun calculateRemainingTime(expiryTime: Long, serverTime: Long): String {
    val remainingMillis = expiryTime - serverTime
    if (remainingMillis <= 0) return "已过期"
    
    val days = remainingMillis / (24 * 60 * 60 * 1000)
    val hours = (remainingMillis % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000)
    return "${days}天${hours}小时"
}

@Composable
private fun ClickableText(
    text: String,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .padding(vertical = 8.dp),
        horizontalArrangement = Arrangement.Start,
        verticalAlignment = Alignment.CenterVertically
    ) {
        val icon = when {
            text.startsWith("QQ") -> Icons.Default.Message
            text.startsWith("微信") -> Icons.Default.Message
            text.startsWith("电话") -> Icons.Default.Phone
            else -> null
        }
        
        icon?.let {
            Icon(
                imageVector = it,
                contentDescription = null,
                modifier = Modifier.size(24.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
        }
        
        Text(text = text)
    }
}

@Composable
private fun ContactRow(
    type: String,
    value: String,
    onCopy: (String) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Start,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "$type：",
            style = MaterialTheme.typography.bodyMedium
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium.copy(
                color = MaterialTheme.colorScheme.primary,
                fontWeight = FontWeight.Bold
            ),
            modifier = Modifier
                .clip(RoundedCornerShape(4.dp))
                .clickable(
                    interactionSource = remember { MutableInteractionSource() },
                    indication = rememberRipple(bounded = true)
                ) {
                    onCopy(value)
                }
                .padding(horizontal = 8.dp, vertical = 4.dp)
        )
    }
}

