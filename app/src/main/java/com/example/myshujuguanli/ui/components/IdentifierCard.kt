package com.example.myshujuguanli.ui.components

import android.content.Context
import android.view.Gravity
import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp

// 定义常量
private const val PREFS_NAME = "identifier_prefs"
private const val KEY_IDENTIFIERS = "identifiers"
private const val KEY_SELECTED_IDENTIFIER = "selected_identifier"
private const val KEY_IDENTIFIER_REBATES = "identifier_rebates"

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun IdentifierCard(
    identifiers: List<String>,
    selectedIdentifier: String,
    onIdentifierSelected: (String) -> Unit,
    onAddIdentifier: () -> Unit,
    onDeleteIdentifier: (String) -> Unit,
    onIdentifierUpdate: (String, String, Float) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var showDeleteConfirmDialog by remember { mutableStateOf<String?>(null) }
    var showDeleteAllConfirmDialog by remember { mutableStateOf(false) }
    var identifierToEdit by remember { mutableStateOf<String?>(null) }

    // 修改 LaunchedEffect 的逻辑
    LaunchedEffect(Unit) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val savedIdentifiers = prefs.getStringSet(KEY_IDENTIFIERS, setOf()) ?: setOf()
        
        // 只在标识为空时恢复保存的标识
        if (identifiers.isEmpty() && savedIdentifiers.isNotEmpty()) {
            savedIdentifiers.forEach { identifier ->
                onIdentifierUpdate("", identifier, 0f)
            }
            
            // 恢复选中的标识
            val savedSelectedIdentifier = prefs.getString(KEY_SELECTED_IDENTIFIER, "") ?: ""
            if (savedSelectedIdentifier.isNotEmpty()) {
                onIdentifierSelected(savedSelectedIdentifier)
            }
        }
    }

    // 添加标识更新函数
    fun updateIdentifiersInPrefs(newIdentifiers: Set<String>) {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            .edit()
            .putStringSet(KEY_IDENTIFIERS, newIdentifiers)
            .apply()
    }

    fun updateSelectedIdentifierInPrefs(identifier: String) {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            .edit()
            .putString(KEY_SELECTED_IDENTIFIER, identifier)
            .apply()
    }

    // 添加返水管理函数
    fun getIdentifierRebate(identifier: String): Float {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        return prefs.getFloat("${KEY_IDENTIFIER_REBATES}_$identifier", 0f)
    }

    fun updateIdentifierRebate(identifier: String, rebate: Float) {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            .edit()
            .putFloat("${KEY_IDENTIFIER_REBATES}_$identifier", rebate)
            .apply()
    }

    Card(
        modifier = modifier,
        shape = RoundedCornerShape(10.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.8f)
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxHeight()
                .width(30.dp)
                .padding(vertical = 6.dp),
            verticalArrangement = Arrangement.spacedBy(6.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            IconButton(
                onClick = onAddIdentifier,
                modifier = Modifier
                    .size(20.dp)
                    .background(
                        color = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.95f),
                        shape = CircleShape
                    )
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "添加标签",
                    tint = MaterialTheme.colorScheme.onPrimaryContainer,
                    modifier = Modifier.size(20.dp)
                )
            }

            LazyColumn(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(4.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                items(identifiers) { identifier ->
                    Box(
                        modifier = Modifier
                            .width(20.dp)
                            .background(
                                if (identifier == selectedIdentifier)
                                    MaterialTheme.colorScheme.primaryContainer
                                else MaterialTheme.colorScheme.surface,
                                RoundedCornerShape(3.dp)
                            )
                            .combinedClickable(
                                onClick = { 
                                    if (identifier == selectedIdentifier) {
                                        onIdentifierSelected("")
                                    } else {
                                        onIdentifierSelected(identifier)
                                    }
                                },
                                onLongClick = { identifierToEdit = identifier }
                            )
                            .padding(horizontal = 2.dp, vertical = 2.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = identifier,
                                style = MaterialTheme.typography.bodySmall.copy(
                                    fontWeight = FontWeight.Bold
                                ),
                                textAlign = TextAlign.Center,
                                color = if (identifier == selectedIdentifier)
                                    MaterialTheme.colorScheme.onPrimaryContainer
                                else MaterialTheme.colorScheme.onSurface,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .wrapContentWidth(Alignment.CenterHorizontally)
                            )
                            // 返水显示已移除
                        }
                    }
                }
            }
        }
    }

    // 编辑对话框
    identifierToEdit?.let { identifier ->
        EditIdentifierDialog(
            currentIdentifier = identifier,
            currentRebate = getIdentifierRebate(identifier),
            onDismiss = { identifierToEdit = null },
            onEdit = { newName, newRebate -> 
                onIdentifierUpdate(identifier, newName, newRebate)
                identifierToEdit = null
            },
            onDelete = { 
                showDeleteConfirmDialog = identifier
                identifierToEdit = null
            },
            onDeleteAll = {
                showDeleteAllConfirmDialog = true
                identifierToEdit = null
            }
        )
    }

    // 单个标识删除确认对话框
    showDeleteConfirmDialog?.let { identifier ->
        AlertDialog(
            onDismissRequest = { showDeleteConfirmDialog = null },
            title = { Text("删除标签") },
            text = { Text("确定要删除标签 \"$identifier\" 吗？") },
            confirmButton = {
                TextButton(
                    onClick = {
                        onDeleteIdentifier(identifier)
                        if (identifier == selectedIdentifier) {
                            onIdentifierSelected("")
                        }
                        showDeleteConfirmDialog = null
                    }
                ) {
                    Text("确定")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteConfirmDialog = null }) {
                    Text("取消")
                }
            }
        )
    }

    // 删除全部确认对话框
    if (showDeleteAllConfirmDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteAllConfirmDialog = false },
            title = { Text("删除全部标签") },
            text = { Text("确定要删除所有标签吗？此操作不可恢复。") },
            confirmButton = {
                TextButton(
                    onClick = {
                        // 清除 SharedPreferences 中的标识数据
                        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                            .edit()
                            .remove(KEY_IDENTIFIERS)
                            .remove(KEY_SELECTED_IDENTIFIER)
                            .apply()
                        
                        // 清除选中的标识
                        onIdentifierSelected("")
                        
                        // 删除所有标识
                        identifiers.toList().forEach { id ->
                            onDeleteIdentifier(id)
                        }
                        
                        showDeleteAllConfirmDialog = false
                        
                        Toast.makeText(
                            context,
                            "已删除所有标签",
                            Toast.LENGTH_SHORT
                        ).apply { 
                            setGravity(Gravity.CENTER, 0, 0) 
                        }.show()
                    }
                ) {
                    Text("确定删除全部")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteAllConfirmDialog = false }) {
                    Text("取消")
                }
            }
        )
    }
} 