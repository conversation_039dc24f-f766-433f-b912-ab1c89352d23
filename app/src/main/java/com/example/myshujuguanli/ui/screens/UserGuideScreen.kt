package com.example.myshujuguanli.ui.screens

import android.content.Context
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilledIconButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.launch
import java.io.BufferedReader
import java.io.InputStreamReader

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun UserGuideScreen(
    onNavigateBack: () -> Unit
) {
    var rawGuideContent by remember { mutableStateOf<List<String>>(emptyList()) }
    val context = LocalContext.current
    val listState = rememberLazyListState()
    val coroutineScope = rememberCoroutineScope()
    
    // 搜索相关状态
    var searchQuery by remember { mutableStateOf("") }
    var searchResults by remember { mutableStateOf<List<Int>>(emptyList()) }
    var currentSearchIndex by remember { mutableIntStateOf(-1) }
    
    // 记录上一次的滚动状态
    var previousOffset by remember { mutableIntStateOf(0) }
    var previousIndex by remember { mutableIntStateOf(0) }

    // 检测滚动状态
    val isScrollingUp by remember {
        derivedStateOf {
            val firstVisibleItemIndex = listState.firstVisibleItemIndex
            val firstVisibleItemScrollOffset = listState.firstVisibleItemScrollOffset
            
            val isScrollingUp = when {
                firstVisibleItemIndex != previousIndex -> firstVisibleItemIndex < previousIndex
                else -> firstVisibleItemScrollOffset <= previousOffset
            }
            
            previousOffset = firstVisibleItemScrollOffset
            previousIndex = firstVisibleItemIndex
            
            !isScrollingUp
        }
    }

    LaunchedEffect(Unit) {
        rawGuideContent = readGuideFile(context)
    }

    // 处理搜索逻辑
    LaunchedEffect(searchQuery) {
        if (searchQuery.isNotEmpty()) {
            searchResults = rawGuideContent.mapIndexedNotNull { index, line ->
                if (line.contains(searchQuery, ignoreCase = true)) index else null
            }
            currentSearchIndex = if (searchResults.isNotEmpty()) 0 else -1
        } else {
            searchResults = emptyList()
            currentSearchIndex = -1
        }
    }

    val guideContent = rawGuideContent.map { line ->
       
        buildAnnotatedString {
            when {
                line.contains("[color=") -> {
                    // 自定义颜色文本
                    try {
                        var currentText = line.replace("\n", "") // 移除换行符
                       
                        
                        // 如果是列表项，先添加列表符号
                        if (currentText.startsWith("- ")) {
                            append("• ")
                            currentText = currentText.substring(2)
                        }
                        
                        while (currentText.contains("[color=")) {
                            val startIndex = currentText.indexOf("[color=")
                            val colorStartIndex = startIndex + 7
                            val colorEndIndex = currentText.indexOf("]", colorStartIndex)
                            val contentEndIndex = currentText.indexOf("[/color]", colorEndIndex)
                            
                           
                            if (colorEndIndex != -1 && contentEndIndex != -1) {
                                // 添加颜色标记之前的文本
                                append(currentText.substring(0, startIndex))
                                
                                // 获取颜色值和文本内容
                                val colorCode = currentText.substring(colorStartIndex, colorEndIndex)
                                val content = currentText.substring(colorEndIndex + 1, contentEndIndex)
                                
                               
                                
                                // 应用颜色样式
                                try {
                                    val color = Color(android.graphics.Color.parseColor(colorCode))
                                    withStyle(SpanStyle(color = color)) {
                                        append(content)
                                    }
                                } catch (e: Exception) {
                              
                                    e.printStackTrace()
                                    append(content)
                                }
                                
                                // 更新剩余文本
                                currentText = if (contentEndIndex + 8 < currentText.length) {
                                    currentText.substring(contentEndIndex + 8)
                                } else {
                                    ""
                                }
                            } else {
                                // 如果标记不完整，直接添加剩余文本
                       
                                append(currentText)
                                break
                            }
                        }
                        // 添加任何剩余的文本
                        if (currentText.isNotEmpty()) {
                            append(currentText)
                        }
                    } catch (e: Exception) {
                        
                        e.printStackTrace()
                        // 如果解析出错，直接显示原文
                        if (line.startsWith("- ")) {
                            append("• ${line.substring(2)}")
                        } else {
                            append(line)
                        }
                    }
                }
                line.startsWith("# ") -> {
                   
                    // H1 标题
                    withStyle(
                        SpanStyle(
                            fontWeight = FontWeight.Bold,
                            fontSize = 24.sp,
                            color = MaterialTheme.colorScheme.primary
                        )
                    ) {
                        append(line.substring(2))
                    }
                }
                line.startsWith("## ") -> {
                    
                    // H2 标题
                    withStyle(
                        SpanStyle(
                            fontWeight = FontWeight.Bold,
                            fontSize = 20.sp,
                            color = MaterialTheme.colorScheme.secondary
                        )
                    ) {
                        append(line.substring(3))
                    }
                }
                line.startsWith("### ") -> {
                   
                    // H3 标题
                    withStyle(
                        SpanStyle(
                            fontWeight = FontWeight.Bold,
                            fontSize = 18.sp,
                            color = MaterialTheme.colorScheme.tertiary
                        )
                    ) {
                        append(line.substring(4))
                    }
                }
                line.startsWith("- ") -> {
                   
                    // 列表项
                    append("• ")
                    append(line.substring(2))
                }
                line.contains("**") -> {
                    // 粗体文本
                    val parts = line.split("**")
                    parts.forEachIndexed { index, part ->
                        if (index % 2 == 1) {
                            withStyle(SpanStyle(fontWeight = FontWeight.Bold)) {
                                append(part)
                            }
                        } else {
                            append(part)
                        }
                    }
                }
                line.contains("*") -> {
                    // 斜体文本
                    val parts = line.split("*")
                    parts.forEachIndexed { index, part ->
                        if (index % 2 == 1) {
                            withStyle(SpanStyle(fontStyle = FontStyle.Italic)) {
                                append(part)
                            }
                        } else {
                            append(part)
                        }
                    }
                }
                line.contains("~~") -> {
                    // 删除线文本
                    val parts = line.split("~~")
                    parts.forEachIndexed { index, part ->
                        if (index % 2 == 1) {
                            withStyle(SpanStyle(textDecoration = TextDecoration.LineThrough)) {
                                append(part)
                            }
                        } else {
                            append(part)
                        }
                    }
                }
                else -> {
                   
                    append(line)
                }
            }
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("使用说明") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(modifier = Modifier.fillMaxSize()) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
            ) {
                // 搜索框区域
                AnimatedVisibility(
                    visible = !isScrollingUp,
                    enter = expandVertically(
                        expandFrom = Alignment.Top,
                        animationSpec = spring(
                            dampingRatio = Spring.DampingRatioLowBouncy,
                            stiffness = Spring.StiffnessLow
                        )
                    ) + fadeIn(),
                    exit = shrinkVertically(
                        shrinkTowards = Alignment.Top,
                        animationSpec = tween(durationMillis = 300)
                    ) + fadeOut()
                ) {
                    Surface(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp, vertical = 8.dp),
                        shape = RoundedCornerShape(28.dp),
                        color = MaterialTheme.colorScheme.surface,
                        tonalElevation = 3.dp,
                        shadowElevation = 3.dp
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(56.dp)
                                .padding(horizontal = 4.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            IconButton(
                                onClick = { /* 搜索图标点击事件 */ },
                                modifier = Modifier.padding(start = 4.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Search,
                                    contentDescription = "搜索",
                                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                            BasicTextField(
                                value = searchQuery,
                                onValueChange = { searchQuery = it },
                                modifier = Modifier
                                    .weight(1f)
                                    .padding(horizontal = 8.dp),
                                textStyle = TextStyle(
                                    color = MaterialTheme.colorScheme.onSurface,
                                    fontSize = 16.sp
                                ),
                                singleLine = true,
                                cursorBrush = SolidColor(MaterialTheme.colorScheme.primary),
                                decorationBox = { innerTextField ->
                                    Box(
                                        modifier = Modifier.fillMaxSize(),
                                        contentAlignment = Alignment.CenterStart
                                    ) {
                                        if (searchQuery.isEmpty()) {
                                            Text(
                                                "搜索内容",
                                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                                style = MaterialTheme.typography.bodyLarge
                                            )
                                        }
                                        innerTextField()
                                    }
                                }
                            )
                            if (searchQuery.isNotEmpty()) {
                                IconButton(
                                    onClick = { searchQuery = "" },
                                    modifier = Modifier.padding(end = 4.dp)
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Close,
                                        contentDescription = "清除",
                                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                }
                            }
                        }
                    }
                }

                // 搜索结果计数和导航
                AnimatedVisibility(
                    visible = searchResults.isNotEmpty(),
                    enter = expandVertically() + fadeIn(),
                    exit = shrinkVertically() + fadeOut()
                ) {
                    Surface(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp),
                        color = MaterialTheme.colorScheme.primaryContainer,
                        shape = RoundedCornerShape(16.dp)
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp, vertical = 8.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "${currentSearchIndex + 1}/${searchResults.size}",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onPrimaryContainer
                            )
                            Row(
                                horizontalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                FilledIconButton(
                                    onClick = {
                                        if (currentSearchIndex > 0) {
                                            currentSearchIndex--
                                            coroutineScope.launch {
                                                listState.animateScrollToItem(searchResults[currentSearchIndex])
                                            }
                                        }
                                    },
                                    enabled = currentSearchIndex > 0,
                                    colors = IconButtonDefaults.filledIconButtonColors(
                                        containerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                                        contentColor = MaterialTheme.colorScheme.primary
                                    )
                                ) {
                                    Icon(Icons.Default.KeyboardArrowUp, "上一个")
                                }
                                FilledIconButton(
                                    onClick = {
                                        if (currentSearchIndex < searchResults.size - 1) {
                                            currentSearchIndex++
                                            coroutineScope.launch {
                                                listState.animateScrollToItem(searchResults[currentSearchIndex])
                                            }
                                        }
                                    },
                                    enabled = currentSearchIndex < searchResults.size - 1,
                                    colors = IconButtonDefaults.filledIconButtonColors(
                                        containerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                                        contentColor = MaterialTheme.colorScheme.primary
                                    )
                                ) {
                                    Icon(Icons.Default.KeyboardArrowDown, "下一个")
                                }
                            }
                        }
                    }
                }

                // 内容列表
                LazyColumn(
                    state = listState,
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .padding(horizontal = 16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(guideContent.size) { index ->
                        val isSearchResult = searchResults.contains(index)
                        Surface(
                            color = if (isSearchResult && index == searchResults[currentSearchIndex]) {
                                MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                            } else {
                                Color.Transparent
                            }
                        ) {
                            Text(
                                text = guideContent[index],
                                modifier = Modifier.padding(vertical = 4.dp)
                            )
                        }
                    }
                    item {
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "版本: 1.0.0",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }
    }
}

private fun readGuideFile(context: Context): List<String> {
    return try {
       
        context.assets.open("docs/guide.txt").use { inputStream ->
            BufferedReader(InputStreamReader(inputStream)).useLines { lines ->
                val result = lines.toList()
               
                result
            }
        }
    } catch (e: Exception) {
       
        e.printStackTrace()
        listOf("无法加载使用说明文件")
    }
} 