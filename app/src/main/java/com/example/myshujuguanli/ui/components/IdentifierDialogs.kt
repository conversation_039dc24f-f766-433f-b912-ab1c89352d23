package com.example.myshujuguanli.ui.components


import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.foundation.layout.*
import androidx.compose.ui.unit.dp
import android.content.Context
import android.widget.Toast
import android.view.Gravity
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.input.KeyboardType



@Composable
fun AddIdentifierDialog(
    identifiers: List<String>,
    onDismiss: () -> Unit,
    onConfirm: (String, Float) -> Unit
) {
    var identifierName by remember { mutableStateOf("") }
    var rebateValue by remember { mutableStateOf("0.0") }
    var errorMessage by remember { mutableStateOf("") }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("添加标签") },
        text = {
            Column {
                OutlinedTextField(
                    value = identifierName,
                    onValueChange = { 
                        identifierName = it
                        errorMessage = ""
                    },
                    label = { Text("标签名称") },
                    singleLine = true,
                    isError = errorMessage.isNotEmpty(),
                    supportingText = if (errorMessage.isNotEmpty()) {
                        { Text(errorMessage, color = MaterialTheme.colorScheme.error) }
                    } else null
                )
                Spacer(modifier = Modifier.height(8.dp))
                OutlinedTextField(
                    value = rebateValue,
                    onValueChange = { 
                        if (it.isEmpty() || it.matches(Regex("^\\d*\\.?\\d*$"))) {
                            rebateValue = it
                        }
                    },
                    label = { Text("返水值 (%)") },
                    singleLine = true,
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    trailingIcon = { Text("%") }
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    when {
                        identifierName.isBlank() -> {
                            errorMessage = "标签名称不能为空"
                        }
                        identifiers.contains(identifierName) -> {
                            errorMessage = "标签已存在"
                        }
                        else -> {
                            val rebate = rebateValue.toFloatOrNull() ?: 0f
                            onConfirm(identifierName, rebate)
                            onDismiss()
                        }
                    }
                }
            ) {
                Text("确定")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

@Composable
fun DeleteIdentifierDialog(
    identifierToDelete: String,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("删除标签") },
        text = { Text("确定要删除标签 \"$identifierToDelete\" 吗？") },
        confirmButton = {
            TextButton(onClick = {
                onConfirm()
                onDismiss()
            }) {
                Text("确定")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

@Composable
fun EditIdentifierDialog(
    currentIdentifier: String,
    currentRebate: Float,
    onDismiss: () -> Unit,
    onEdit: (String, Float) -> Unit,
    onDelete: () -> Unit,
    onDeleteAll: () -> Unit
) {
    var identifierName by remember { mutableStateOf(currentIdentifier) }
    var rebateValue by remember { mutableStateOf(currentRebate.toString()) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("编辑标签") },
        text = {
            Column {
                OutlinedTextField(
                    value = identifierName,
                    onValueChange = { identifierName = it },
                    label = { Text("标签名称") },
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth()
                )
                Spacer(modifier = Modifier.height(8.dp))
                OutlinedTextField(
                    value = rebateValue,
                    onValueChange = { 
                        if (it.isEmpty() || it.matches(Regex("^\\d*\\.?\\d*$"))) {
                            rebateValue = it
                        }
                    },
                    label = { Text("返水值 (%)") },
                    singleLine = true,
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    trailingIcon = { Text("%") }
                )
                Spacer(modifier = Modifier.height(16.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    TextButton(
                        onClick = onDelete,
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = MaterialTheme.colorScheme.error
                        ),
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("删除此标签")
                    }
                    Spacer(modifier = Modifier.width(8.dp))
                    TextButton(
                        onClick = onDeleteAll,
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = MaterialTheme.colorScheme.error
                        ),
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("删除全部")
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    if (identifierName.isNotBlank()) {
                        val rebate = rebateValue.toFloatOrNull() ?: 0f
                        onEdit(identifierName, rebate)
                    }
                    onDismiss()
                }
            ) {
                Text("保存")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
} 