// package com.example.myshujuguanli.ui.screens

// import androidx.compose.foundation.clickable
// import androidx.compose.foundation.layout.*
// import androidx.compose.foundation.lazy.LazyColumn
// import androidx.compose.foundation.lazy.items
// import androidx.compose.foundation.text.KeyboardActions
// import androidx.compose.foundation.text.KeyboardOptions
// import androidx.compose.material.icons.Icons
// import androidx.compose.material.icons.filled.ArrowBack
// import androidx.compose.material3.*
// import androidx.compose.runtime.*
// import androidx.compose.ui.Alignment
// import androidx.compose.ui.Modifier
// import androidx.compose.ui.platform.LocalContext
// import androidx.compose.ui.text.input.KeyboardType
// import androidx.compose.ui.text.style.TextAlign
// import androidx.compose.ui.unit.dp
// import com.example.myshujuguanli.utils.BettingOdds
// import com.example.myshujuguanli.utils.OddsSettingItem
// import com.example.myshujuguanli.utils.ZodiacOddsSettings
// import androidx.compose.ui.text.font.FontWeight
// import androidx.compose.ui.text.input.ImeAction
// import com.example.myshujuguanli.utils.ZodiacUtils


// @OptIn(ExperimentalMaterial3Api::class)
// @Composable
// fun OddsSettingsScreen(
//     onBack: () -> Unit
// ) {
//     val context = LocalContext.current
//     var currentSettings by remember { mutableStateOf(BettingOdds.getCurrentSettings()) }
//     var showResetConfirmDialog by remember { mutableStateOf(false) }  // 添加确认对话框状态

//     // 在组件初始化时加载保存的赔率设置
//     LaunchedEffect(Unit) {
//         BettingOdds.loadSavedSettings(context)
//         val loadedSettings = BettingOdds.getCurrentSettings()
//         // 如果加载的设置为空，则使用默认设置初始化
//         currentSettings = if (loadedSettings.normalOdds.isEmpty() && loadedSettings.currentYearOdds.isEmpty()) {
//             ZodiacOddsSettings(BettingOdds.DEFAULT_NORMAL_ODDS, BettingOdds.DEFAULT_CURRENT_YEAR_ODDS)
//         } else {
//             loadedSettings
//         }
//     }

//     Scaffold(
//         topBar = {
//             TopAppBar(
//                 title = { Text("赔率设置") },
//                 navigationIcon = {
//                     IconButton(onClick = onBack) {
//                         Icon(Icons.Default.ArrowBack, contentDescription = "返回")
//                     }
//                 },
//                 actions = {
//                     // 重置按钮 - 显示确认对话框
//                     TextButton(onClick = { showResetConfirmDialog = true }) {
//                         Text("重置")
//                     }
//                 }
//             )
//         }
//     ) { paddingValues ->
//         LazyColumn(
//             modifier = Modifier
//                 .fillMaxSize()
//                 .padding(paddingValues)
//                 .padding(horizontal = 16.dp),
//             verticalArrangement = Arrangement.spacedBy(8.dp)
//         ) {
//             // 遍历所有赔率类型
//             items(BettingOdds.DEFAULT_NORMAL_ODDS.keys.toList()) { betType ->
//                 OddsSettingItemCard(
//                     item = OddsSettingItem(
//                         type = betType,
//                         normalOdds = currentSettings.normalOdds[betType] ?: 0.0,
//                         currentYearOdds = currentSettings.currentYearOdds[betType] ?: 0.0
//                     ),
//                     onNormalOddsChange = { newOdds ->
//                         val newNormalOdds = currentSettings.normalOdds + (betType to newOdds)
//                         val newSettings = currentSettings.copy(normalOdds = newNormalOdds)
//                         BettingOdds.updateOddsSettings(context, newSettings)
//                         currentSettings = BettingOdds.getCurrentSettings()
//                     },
//                     onCurrentYearOddsChange = { newOdds ->
//                         // 只有需要本命年赔率的类型才更新
//                         if (betType in BettingOdds.DEFAULT_CURRENT_YEAR_ODDS.keys) {
//                             val newCurrentYearOdds = currentSettings.currentYearOdds + (betType to newOdds)
//                             val newSettings = currentSettings.copy(currentYearOdds = newCurrentYearOdds)
//                             BettingOdds.updateOddsSettings(context, newSettings)
//                             currentSettings = BettingOdds.getCurrentSettings()
//                         }
//                     }
//                 )
//             }
//         }

//         // 添加重置确认对话框
//         if (showResetConfirmDialog) {
//             AlertDialog(
//                 onDismissRequest = { showResetConfirmDialog = false },
//                 title = { Text("确认重置") },
//                 text = { Text("确定要将所有赔率重置为默认值吗？") },
//                 confirmButton = {
//                     TextButton(
//                         onClick = {
//                             val defaultSettings = ZodiacOddsSettings(
//                                 BettingOdds.DEFAULT_NORMAL_ODDS,
//                                 BettingOdds.DEFAULT_CURRENT_YEAR_ODDS
//                             )
//                             BettingOdds.updateOddsSettings(context, defaultSettings)
//                             currentSettings = BettingOdds.getCurrentSettings()
//                             showResetConfirmDialog = false
//                         }
//                     ) {
//                         Text("确定")
//                     }
//                 },
//                 dismissButton = {
//                     TextButton(onClick = { showResetConfirmDialog = false }) {
//                         Text("取消")
//                     }
//                 }
//             )
//         }
//     }
// }

// @Composable
// private fun OddsSettingItemCard(
//     item: OddsSettingItem,
//     onNormalOddsChange: (Double) -> Unit,
//     onCurrentYearOddsChange: (Double) -> Unit
// ) {
//     // 获取context引用
//     val context = LocalContext.current
    
//     // 使用 key 参数来强制重组
//     key(item.normalOdds, item.currentYearOdds) {
//         var tempNormalOdds by remember { mutableStateOf(item.normalOdds.toString()) }
//         var tempCurrentYearOdds by remember { mutableStateOf(item.currentYearOdds.toString()) }
//         var normalOddsError by remember { mutableStateOf<String?>(null) }
//         var currentYearOddsError by remember { mutableStateOf<String?>(null) }

//         // 添加验证和提交函数
//         fun validateAndSubmitOdds(value: String, isNormal: Boolean) {
//             val odds = value.toDoubleOrNull()
//             if (odds != null && odds > 0) {
//                 if (isNormal) {
//                     onNormalOddsChange(odds)
//                     normalOddsError = null
//                 } else {
//                     onCurrentYearOddsChange(odds)
//                     currentYearOddsError = null
//                 }
//             } else {
//                 if (isNormal) {
//                     normalOddsError = if (odds == null) "请输入有效数字" else "赔率必须大于0"
//                 } else {
//                     currentYearOddsError = if (odds == null) "请输入有效数字" else "赔率必须大于0"
//                 }
//             }
//         }

//         Card(
//             modifier = Modifier
//                 .fillMaxWidth()
//                 .padding(vertical = 4.dp),
//             elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
//             colors = CardDefaults.cardColors(
//                 containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.7f)
//             ),
//             shape = MaterialTheme.shapes.medium
//         ) {
//             Row(
//                 modifier = Modifier
//                     .fillMaxWidth()
//                     .padding(horizontal = 16.dp, vertical = 12.dp),
//                 horizontalArrangement = Arrangement.spacedBy(8.dp),
//                 verticalAlignment = Alignment.CenterVertically
//             ) {
//                 // 类型名称
//                 Text(
//                     text = item.type,
//                     style = MaterialTheme.typography.titleMedium.copy(
//                         fontWeight = FontWeight.Bold
//                     ),
//                     color = MaterialTheme.colorScheme.primary,
//                     modifier = Modifier.width(80.dp)
//                 )

//                 // 普通赔率输入框
//                 OutlinedTextField(
//                     value = tempNormalOdds,
//                     onValueChange = { newValue ->
//                         tempNormalOdds = newValue
//                     },
//                     textStyle = MaterialTheme.typography.bodyMedium,
//                     keyboardOptions = KeyboardOptions(
//                         keyboardType = KeyboardType.Decimal,
//                         imeAction = ImeAction.Done
//                     ),
//                     keyboardActions = KeyboardActions(
//                         onDone = {
//                             validateAndSubmitOdds(tempNormalOdds, true)
//                         }
//                     ),
//                     singleLine = true,
//                     isError = normalOddsError != null,
//                     label = { Text("通用赔率") },
//                     colors = OutlinedTextFieldDefaults.colors(
//                         focusedBorderColor = MaterialTheme.colorScheme.primary,
//                         unfocusedBorderColor = MaterialTheme.colorScheme.outline.copy(alpha = 0.5f),
//                         errorBorderColor = MaterialTheme.colorScheme.error
//                     ),
//                     modifier = Modifier.weight(1f)
//                 )

//                 // 本命年赔率输入框（只对需要的类型显示）
//                 if (item.type !in setOf("特码", "二中二", "三中三", "三中二", "平特尾","五不中","六不中","七不中","八不中","九不中","十不中","十一不中","包双单合双单","包波色","包波色双单")) {
//                     OutlinedTextField(
//                         value = tempCurrentYearOdds,
//                         onValueChange = { newValue ->
//                             tempCurrentYearOdds = newValue
//                         },
//                         textStyle = MaterialTheme.typography.bodyMedium,
//                         keyboardOptions = KeyboardOptions(
//                             keyboardType = KeyboardType.Decimal,
//                             imeAction = ImeAction.Done
//                         ),
//                         keyboardActions = KeyboardActions(
//                             onDone = {
//                                 validateAndSubmitOdds(tempCurrentYearOdds, false)
//                             }
//                         ),
//                         singleLine = true,
//                         isError = currentYearOddsError != null,
//                         label = { Text("有(${ZodiacUtils.getZodiacForYear(ZodiacUtils.getCurrentBaseYear(context))})赔率") },
//                         colors = OutlinedTextFieldDefaults.colors(
//                             focusedBorderColor = MaterialTheme.colorScheme.primary,
//                             unfocusedBorderColor = MaterialTheme.colorScheme.outline.copy(alpha = 0.5f),
//                             errorBorderColor = MaterialTheme.colorScheme.error
//                         ),
//                         modifier = Modifier.weight(1f)
//                     )
//                 }
//             }
//         }
//     }
// }
