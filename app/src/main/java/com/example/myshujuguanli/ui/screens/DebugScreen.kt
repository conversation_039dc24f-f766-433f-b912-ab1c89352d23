package com.example.myshujuguanli.ui.screens

import android.content.ClipboardManager
import android.content.Context
import android.view.Gravity
import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import com.example.myshujuguanli.utils.DataParser
import androidx.compose.runtime.Composable
import androidx.compose.foundation.border

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DebugScreen(
    onNavigateBack: () -> Unit
) {
    var inputText by remember { mutableStateOf("") }
    var debugOutput by remember { mutableStateOf<AnnotatedString>(AnnotatedString("")) }
    val context = LocalContext.current

    // 在Composable环境中捕获颜色
    val primaryColor = MaterialTheme.colorScheme.primary
    val errorColor = MaterialTheme.colorScheme.error
    val tertiaryColor = MaterialTheme.colorScheme.tertiary

    // 定义解析和格式化文本的函数
    val parseAndFormatOutput = { text: String ->
        // 使用DataParser解析输入文本
        DataParser.parseInput(text)
        
        // 获取清洗和分割后的文本，以及匹配到的文本
        val cleanedText = DataParser.getDebugCleanedText()
        val tempLines = DataParser.getDebugTempLine()
        val matchedTexts = DataParser.getDebugMatchedTexts()
        
        // 创建带有颜色标记的输出文本
        buildAnnotatedString {
            withStyle(style = SpanStyle(color = primaryColor, fontWeight = FontWeight.Bold)) {
                append("=== 清洗后的文本(转换标点符号) ===\n")
            }
            
            // 对清洗后的文本正常显示，不标记错误
            val cleanedTextLines = cleanedText.split("\n")
            cleanedTextLines.forEach { line ->
                if (line.isNotEmpty()) {
                    append("$line\n")
                } else {
                    append("\n")
                }
            }

            append("\n")
            withStyle(style = SpanStyle(color = primaryColor, fontWeight = FontWeight.Bold)) {
                append("=== 处理后的文本(分割成多行) ===\n")
            }

            // 对分割后的文本直接显示，不再尝试匹配验证
            val tempLinesArray = tempLines.split("\n")
            tempLinesArray.forEach { line ->
                if (line.isNotEmpty()) {
                    append("$line\n")
                } else {
                    append("\n")
                }
            }
            
            // 添加匹配到的特殊输入展示
            append("\n")
            withStyle(style = SpanStyle(color = primaryColor, fontWeight = FontWeight.Bold)) {
                append("=== 成功匹配到的文本 ===\n")
            }
            
            // 显示DataParser中收集到的匹配文本
            val matchedTextLines = matchedTexts.split("\n").filter { it.isNotBlank() }
            if (matchedTextLines.isEmpty()) {
                withStyle(style = SpanStyle(color = errorColor)) {
                    append("无匹配到的文本\n")
                }
            } else {
                matchedTextLines.forEach { line ->
                    withStyle(style = SpanStyle(color = tertiaryColor)) {
                        append("$line \n")
                    }
                }
            }
        }
    }

    Scaffold(
        topBar = {
            CenterAlignedTopAppBar(
                title = { 
                    Text(
                        "调试界面",
                        style = MaterialTheme.typography.titleLarge
                    ) 
                },
                navigationIcon = {
                    TextButton(
                        onClick = onNavigateBack,
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = MaterialTheme.colorScheme.primary
                        )
                    ) {
                        Text("返回")
                    }
                }
            )
        },
        containerColor = MaterialTheme.colorScheme.background
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 16.dp, vertical = 8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 输入框
            OutlinedTextField(
                value = inputText,
                onValueChange = { inputText = it },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(120.dp),
                label = { Text("输入文本") },
                shape = RoundedCornerShape(12.dp),
                colors = TextFieldDefaults.outlinedTextFieldColors(
                    focusedBorderColor = MaterialTheme.colorScheme.primary,
                    unfocusedBorderColor = MaterialTheme.colorScheme.outline
                )
            )

            // 按钮行
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                ActionButton(
                    text = "重试",
                    modifier = Modifier.weight(1f),
                    onClick = {
                        debugOutput = parseAndFormatOutput(inputText)
                    }
                )
                
                ActionButton(
                    text = "清空",
                    modifier = Modifier.weight(1f),
                    onClick = { 
                        inputText = ""
                        debugOutput = AnnotatedString("")
                    }
                )
                
                ActionButton(
                    text = "复制",
                    modifier = Modifier.weight(1f),
                    onClick = {
                        if (inputText.isNotEmpty()) {
                            val clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                            clipboard.setText(inputText)
                            Toast.makeText(context, "已复制输入文本到剪贴板", Toast.LENGTH_SHORT).apply {
                                setGravity(Gravity.CENTER, 0, 0)
                            }.show()
                        }
                    }
                )
                
                ActionButton(
                    text = "粘贴",
                    modifier = Modifier.weight(1f),
                    onClick = {
                        val clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                        clipboard.primaryClip?.getItemAt(0)?.text?.let {
                            inputText = it.toString()
                            debugOutput = parseAndFormatOutput(inputText)
                        }
                    }
                )
            }

            // 输出框
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .padding(vertical = 8.dp)
                    .border(
                        width = 1.dp,
                        color = MaterialTheme.colorScheme.outline.copy(alpha = 0.5f),
                        shape = RoundedCornerShape(12.dp)
                    )
                    .clip(RoundedCornerShape(12.dp))
            ) {
                Text(
                    text = debugOutput,
                    modifier = Modifier
                        .fillMaxSize()
                        .verticalScroll(rememberScrollState())
                        .padding(16.dp),
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}

@Composable
private fun ActionButton(
    text: String,
    modifier: Modifier = Modifier,
    onClick: () -> Unit
) {
    Button(
        onClick = onClick,
        modifier = modifier
            .height(48.dp),
        shape = RoundedCornerShape(24.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = MaterialTheme.colorScheme.primary,
            contentColor = MaterialTheme.colorScheme.onPrimary
        )
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium,
            textAlign = TextAlign.Center
        )
    }
}

// 这个函数将被移除，因为所有功能已经移动到上面的@Composable函数中
// private fun parseAndDisplayOutput(
//     inputText: String, 
//     onOutputReady: (AnnotatedString) -> Unit
// ) {
//     // 此函数内容已移至createFormattedOutput
// } 