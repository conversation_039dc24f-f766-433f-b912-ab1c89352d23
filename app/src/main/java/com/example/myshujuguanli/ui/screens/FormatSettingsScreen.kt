package com.example.myshujuguanli.ui.screens

import android.content.Context
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.ui.Alignment
import com.example.myshujuguanli.utils.ParseLearningEngine
import androidx.compose.ui.platform.LocalContext

@Composable
fun FormatSettingsScreen() {
    // 获取 Context
    val context = LocalContext.current
    
    var originalInput by remember { mutableStateOf("") }
    var correctedInput by remember { mutableStateOf("") }
    
    // 获取已学习的映射
    val mappings by remember { mutableStateOf(ParseLearningEngine.getAllMappings()) }
    
    // 自动纠正开关状态
    var autoCorrectEnabled by remember { mutableStateOf(true) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 使用相同的卡片高度
        val cardHeight = 72.dp
        
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .height(cardHeight),
            elevation = CardDefaults.cardElevation(4.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 16.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "自动纠正格式",
                    style = MaterialTheme.typography.titleMedium
                )
                Switch(
                    checked = autoCorrectEnabled,
                    onCheckedChange = { 
                        autoCorrectEnabled = it
                        // 保存设置
                    }
                )
            }
        }
        
        // 添加说明文本
        Text(
            text = if (autoCorrectEnabled) 
                "自动纠正已启用，输入时将自动应用学习到的规则" 
            else 
                "自动纠正已关闭，输入时将保持原始格式",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        Divider(modifier = Modifier.padding(bottom = 16.dp))
        
        // 原始输入
        TextField(
            value = originalInput,
            onValueChange = { 
                originalInput = it
                ParseLearningEngine.recordOriginalInput(it)
            },
            label = { Text("原始输入 (包含需要去除的符号)") },
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 8.dp)
        )
        
        // 修正后的输入
        TextField(
            value = correctedInput,
            onValueChange = { correctedInput = it },
            label = { Text("修正后的输入 (去除了不需要的符号)") },
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp)
        )
        
        Button(
            onClick = {
                if (originalInput.isNotEmpty() && correctedInput.isNotEmpty()) {
                    ParseLearningEngine.tryLearnNewMappings(originalInput, correctedInput)
                }
            },
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp)
        ) {
            Text("学习符号替换规则")
        }
        
        // 显示当前已学习的规则
        Text(
            text = "已学习的符号替换规则:",
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        // 显示学习到的符号映射
        LazyColumn {
            items(mappings.toList()) { (original, replacement) ->
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp)
                ) {
                    Row(
                        modifier = Modifier
                            .padding(8.dp)
                            .fillMaxWidth()
                    ) {
                        Text("原始符号: '$original'")
                        Spacer(modifier = Modifier.width(16.dp))
                        Text("替换为: '$replacement'")
                    }
                }
            }
        }
    }
} 