package com.example.myshujuguanli

import android.app.Application
import android.content.Context
import com.example.myshujuguanli.utils.ParseLearningEngine
import com.example.myshujuguanli.utils.DataParser

class MyApplication : Application() {
    companion object {
        var isColdStart = true
    }
    
    override fun onCreate() {
        super.onCreate()
        // 应用启动时重置为冷启动状态
        isColdStart = true
        
        ParseLearningEngine.init(this)
        DataParser.init(this)
    }
} 