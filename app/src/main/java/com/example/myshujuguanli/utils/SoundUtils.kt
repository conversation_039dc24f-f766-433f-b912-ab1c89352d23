package com.example.myshujuguanli.utils

import android.media.AudioManager
import android.media.RingtoneManager
import android.content.Context
import android.media.MediaPlayer
import android.net.Uri

object SoundUtils {
    private var mediaPlayer: MediaPlayer? = null

    // 初始化 MediaPlayer
    fun init() {
        if (mediaPlayer == null) {
            mediaPlayer = MediaPlayer()
        }
    }

    // 播放保存成功提示音
    fun playSuccessSound(context: Context? = null) {
        try {
            context?.let {
                // 获取默认短信提示音
                val notification: Uri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
                mediaPlayer?.apply {
                    reset()
                    setDataSource(context, notification)
                    setAudioStreamType(AudioManager.STREAM_NOTIFICATION)
                    prepare()
                    start()
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    // 释放资源
    fun release() {
        mediaPlayer?.apply {
            if (isPlaying) {
                stop()
            }
            release()
        }
        mediaPlayer = null
    }
}
