package com.example.myshujuguanli.utils

import androidx.compose.ui.graphics.Color

enum class NumberColor(
    val background: Color,
    val border: Color,
    val text: Color
) {
    RED(
        background = Color(0xFFFFF0F0),  // 更浅的红色背景
        border = Color(0xFFFF6B6B),      // 明亮的红色边框
        text = Color(0xFFD32F2F)         // 深红色文字
    ),
    BLUE(
        background = Color(0xFFF0F0FF),  // 更浅的蓝色背景
        border = Color(0xFF6B6BFF),      // 明亮的蓝色边框
        text = Color(0xFF1976D2)         // 深蓝色文字
    ),
    GREEN(
        background = Color(0xFFF0FFF0),  // 更浅的绿色背景
        border = Color(0xFF6BFF6B),      // 明亮的绿色边框
        text = Color(0xFF388E3C)         // 深绿色文字
    ),
    NONE(
        background = Color(0xFFF8F8F8),  // 浅灰色背景
        border = Color(0xFFE0E0E0),      // 灰色边框
        text = Color(0xFF757575)         // 深灰色文字
    )
} 