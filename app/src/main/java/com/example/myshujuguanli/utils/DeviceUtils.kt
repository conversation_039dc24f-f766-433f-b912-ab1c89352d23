package com.example.myshujuguanli.utils

import android.content.Context
import android.content.res.Configuration

object DeviceUtils {
    fun isTablet(context: Context): <PERSON><PERSON>an {
        return ((context.resources.configuration.screenLayout 
            and Configuration.SCREENLAYOUT_SIZE_MASK) 
            >= Configuration.SCREENLAYOUT_SIZE_LARGE)
    }
    
    fun isLandscape(context: Context): <PERSON><PERSON>an {
        return context.resources.configuration.orientation == 
            Configuration.ORIENTATION_LANDSCAPE
    }
} 