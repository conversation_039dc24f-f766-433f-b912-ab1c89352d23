import android.content.Context
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteOpenHelper

class DatabaseHelper(context: Context) : SQLiteOpenHelper(context, DATABASE_NAME, null, DATABASE_VERSION) {
    companion object {
        const val DATABASE_NAME = "注额管理.db"
        const val DATABASE_VERSION = 4  // 增加版本号
        const val TABLE_NAME = "注额管理"

        private const val SQL_CREATE_TABLE = """
            CREATE TABLE IF NOT EXISTS $TABLE_NAME (
                特码 INTEGER,
                注额 INTEGER,
                计数 INTEGER,
                地区 TEXT,
                原始数据 TEXT,
                特殊组合注额 INTEGER,
                特殊组合类型 TEXT,
                标识 TEXT,
                时间戳 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                投注类型 TEXT DEFAULT 'normal',
                号码列表 TEXT
            )
        """

        private const val SQL_CREATE_INDEXES = """
            CREATE INDEX IF NOT EXISTS idx_region ON ${TABLE_NAME}(地区);
            CREATE INDEX IF NOT EXISTS idx_special_number ON ${TABLE_NAME}(特码);
            CREATE INDEX IF NOT EXISTS idx_bet_type ON ${TABLE_NAME}(投注类型);
        """
    }

    override fun onCreate(db: SQLiteDatabase) {
        db.execSQL(SQL_CREATE_TABLE)
        db.execSQL(SQL_CREATE_INDEXES)
    }

    override fun onUpgrade(db: SQLiteDatabase, oldVersion: Int, newVersion: Int) {
        if (oldVersion < 3) {
            db.execSQL("ALTER TABLE $TABLE_NAME ADD COLUMN 投注类型 TEXT DEFAULT 'normal'")
        }
        if (oldVersion < 4) {
            try {
                db.execSQL("ALTER TABLE $TABLE_NAME ADD COLUMN 号码列表 TEXT")
            } catch (e: Exception) {
                // 如果列已存在，忽略错误
                e.printStackTrace()
            }
        }
    }
} 