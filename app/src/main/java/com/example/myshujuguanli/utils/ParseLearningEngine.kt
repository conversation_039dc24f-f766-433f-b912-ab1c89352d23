package com.example.myshujuguanli.utils

import android.content.Context
import android.util.Log
import com.example.myshujuguanli.utils.DataParser.calculateSimilarity
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.io.File

object ParseLearningEngine {
    private const val TAG = "ParseLearningEngine"
    private const val MAPPINGS_FILE = "symbol_mappings.json"
    private const val LAST_LEARNED_KEY = "last_learned_rule"
    private var appContext: Context? = null
    private var mappings = mutableMapOf<String, String>()
    private var lastLearnedRule: String? = null
    
    // 缓存已排序的规则，只在规则变化时更新
    private var sortedRules: List<Map.Entry<String, String>> = emptyList()
    
    private fun isChineseChar(c: Char) = c.toString().matches(Regex("[\u4e00-\u9fa5]"))
    
    private fun isSymbol(c: Char) = !c.isLetterOrDigit() && !c.isWhitespace() && !isChineseChar(c)
    
    fun init(context: Context) {
        appContext = context.applicationContext
        loadMappings()
        updateSortedRules() // 初始化时排序规则
        lastLearnedRule = appContext?.getSharedPreferences("settings", Context.MODE_PRIVATE)
            ?.getString(LAST_LEARNED_KEY, null)
    }
    
    private fun shouldAutoCorrect() = appContext?.getSharedPreferences("settings", Context.MODE_PRIVATE)
        ?.getBoolean("auto_correct_enabled", true) ?: true
    
    // 更新排序后的规则缓存
    private fun updateSortedRules() {
        sortedRules = mappings.entries.sortedByDescending { it.key.length }
    }
    
    fun tryAutoCorrect(input: String): String {
        if (!shouldAutoCorrect()) return input

        var result = input
        var lastResult = ""
        var iterations = 0
        val maxIterations = 3
        val appliedRules = mutableListOf<String>()
        
        while (result != lastResult && iterations < maxIterations) {
            lastResult = result
            iterations++
            
            // 1. 处理数字之间的分隔符（特殊通用规则）
            var i = 0
            while (i < result.length) {
                if (i > 0 && i < result.length - 1) {
                    val prevChar = result[i - 1]
                    val currentChar = result[i]
                    val nextChar = result[i + 1]
                    
                    if (prevChar.isDigit() && nextChar.isDigit() && !currentChar.isDigit()) {
                        // 修改这里：检查当前字符是否在已有映射中存在
                        val symbolExists = mappings.keys.any { it == currentChar.toString() }
                        
                        // 只有在映射中不存在该符号规则时才应用默认的点替换规则
                        if (!symbolExists && (!currentChar.isLetterOrDigit() || currentChar == '一' || currentChar == '二')) {
                            val oldResult = result
                            result = result.substring(0, i) + "." + result.substring(i + 1)
                            if (oldResult != result) {
                                appliedRules.add("数字间分隔符规则: '${currentChar}' -> '.'")
                            }
                        }
                    }
                }
                i++
            }
            
            // 2. 应用已排序的规则
            sortedRules.forEach { (pattern, replacement) ->
                if (result.contains(pattern)) {
                    val oldResult = result
                    result = result.replace(pattern, replacement)
                    if (oldResult != result) {
                        appliedRules.add("规则: '$pattern' -> '$replacement'")
                    }
                }
            }
        }
        
        // // 打印应用的规则信息
        // if (appliedRules.isNotEmpty()) {
        //     Log.d(TAG, "应用于文本 '$input' -> '$result' 的规则:")
        //     appliedRules.forEach { rule ->
        //         Log.d(TAG, rule)
        //     }
        // }
        
        return result
    }
    
    fun tryLearnNewMappings(originalText: String, successText: String) {
        // if (!shouldAutoCorrect()) return

        // val changes = findTextChanges(originalText, successText)
        // var hasNewMappings = false
        
        // changes.forEach { (from, to) ->
        //     if (from.isNotEmpty() && from != to && !mappings.containsKey(from)) {
        //         val testText = originalText.replace(from, to)
        //         val similarity = calculateSimilarity(testText, successText)
                
        //         if (similarity > 0.5) {
        //             mappings[from] = to
        //             lastLearnedRule = from
        //             hasNewMappings = true
        //         }
        //     }
        // }
        
        // if (hasNewMappings) {
        //     updateSortedRules()
        //     saveMappings()
        // }
        return
    }
    
    private fun findTextChanges(original: String, success: String): Map<String, String> {
        val changes = mutableMapOf<String, String>()
        
        // Log.d(TAG, "开始分析文本变化:")
        // Log.d(TAG, "原始文本: '$original'")
        // Log.d(TAG, "目标文本: '$success'")
        
        // 首先检查是否有符号被替换为中文的情况
        // 例如：10/20/200 -> 10/20各200，检测 / -> 各 的替换
        val symbolReplacements = mutableMapOf<Char, Char>()
        
        // 比较原始文本和成功文本，查找符号到中文的替换
        var i = 0
        var j = 0
        while (i < original.length && j < success.length) {
            if (original[i] == success[j]) {
                i++
                j++
            } else {
                // 如果在原始文本中找到符号，但在成功文本中对应位置是中文
                if (i < original.length && j < success.length && 
                    isSymbol(original[i]) && isChineseChar(success[j])) {
                    
                    symbolReplacements[original[i]] = success[j]
                    // Log.d(TAG, "检测到可能的符号->中文替换: '${original[i]}' -> '${success[j]}'")
                    
                    // 创建符号+中文的规则，而不是符号→中文
                    val pattern = "${original[i]}${success[j]}"
                    val replacement = success[j].toString()
                    
                    // 添加到规则中
                    changes[pattern] = replacement
                    // Log.d(TAG, "创建符号+中文上下文规则: '$pattern' -> '$replacement'")
                    
                    // 确保删除可能错误创建的符号→中文规则
                    changes.remove(original[i].toString())
                }
                
                // 移动指针
                i++
                j++
            }
        }
        
        // 将字符串转换为代码点序列进行详细分析
        val originalCodePoints = original.codePoints().toArray()
        i = 0
        while (i < originalCodePoints.size) {
            // 当前字符代码点
            val currentCodePoint = originalCodePoints[i]
            
            // 检查数字之间的符号替换模式
            if (i > 0 && i < originalCodePoints.size - 1) {
                val prevChar = originalCodePoints[i - 1].toChar()
                val nextChar = originalCodePoints[i + 1].toChar()
                
                // 添加调试日志
                // Log.d(TAG, "检查代码点: '${String(Character.toChars(currentCodePoint))}' (Unicode: $currentCodePoint)")
                
                // 如果是数字之间的非数字、非中文、非字母字符
                if (prevChar.isDigit() && nextChar.isDigit() && 
                    !Character.isDigit(currentCodePoint) && !Character.isLetter(currentCodePoint)) {
                    val pattern = String(Character.toChars(currentCodePoint))  // 将代码点转换为字符串
                    
                    // 在成功文本中找到对应的数字对
                    val successIndex = success.indexOf(prevChar)
                    if (successIndex >= 0 && successIndex < success.length - 2) {
                        val nextSuccessIndex = success.indexOf(nextChar, successIndex + 1)
                        // Log.d(TAG, "successIndex: $successIndex")
                        // Log.d(TAG, "nextSuccessIndex: $nextSuccessIndex")
                        
                        if (nextSuccessIndex >= 0 && nextSuccessIndex - successIndex <= 2) {
                            // 获取用户使用的分隔符
                            val replacement = success.substring(successIndex + 1, nextSuccessIndex)
                            // Log.d(TAG, "找到替换字符: '$replacement'")
                            
                            if (replacement.length == 1 && !replacement[0].isDigit()) {
                                changes[pattern] = replacement
                                // Log.d(TAG, "找到数字间符号替换规则: '$pattern' -> '$replacement'")
                            }
                        }
                    }
                }
            }
            
            // 检查符号+中文+符号的模式
            if (i > 0 && i < originalCodePoints.size - 2) {  // 确保有足够的字符
                val nextChar = originalCodePoints[i + 1].toChar()
                val afterNextChar = originalCodePoints[i + 2].toChar()
                
                // 如果是 符号+中文+符号 的模式
                if (!Character.isDigit(currentCodePoint) && !Character.isLetter(currentCodePoint) && 
                    isChineseChar(nextChar) && 
                    !Character.isDigit(afterNextChar.code) && !Character.isLetter(afterNextChar.code)) {
                    
                    val pattern = "${String(Character.toChars(currentCodePoint))}$nextChar$afterNextChar"  // 符号+中文+符号
                    val replacement = nextChar.toString()  // 只保留中文
                    
                    // 如果成功文本中包含这个中文字符但不包含这个完整模式
                    if (success.contains(nextChar) && !success.contains(pattern)) {
                        changes[pattern] = replacement
                        // Log.d(TAG, "找到符号中文符号模式: '$pattern' -> '$replacement'")
                    }
                }
                // 如果只是 符号+中文 的模式
                else if (!Character.isDigit(currentCodePoint) && !Character.isLetter(currentCodePoint) && 
                         isChineseChar(nextChar)) {
                    val pattern = "${String(Character.toChars(currentCodePoint))}$nextChar"  // 符号+中文
                    val replacement = nextChar.toString()  // 只保留中文
                    
                    // 如果成功文本中包含这个中文字符但不包含这个符号
                    if (success.contains(nextChar) && !success.contains(pattern)) {
                        changes[pattern] = replacement
                        // Log.d(TAG, "找到符号中文模式: '$pattern' -> '$replacement'")
                    }
                }
            }
            
            // 检查中文+符号检测部分
            if (i > 0) {
                val prevChar = originalCodePoints[i - 1].toChar()
                val currentChar = String(Character.toChars(currentCodePoint))[0]
                
                // 修正条件，确保正确检测中文+符号
                if (isChineseChar(prevChar) && isSymbol(currentChar)) {
                    val pattern = "$prevChar$currentChar"  // 中文+符号
                    val replacement = prevChar.toString()  // 只保留中文
                    
                    // 如果成功文本中包含这个中文字符但不包含这个完整模式
                    if (success.contains(prevChar) && !success.contains(pattern)) {
                        changes[pattern] = replacement
                        // Log.d(TAG, "找到中文符号模式: '$pattern' -> '$replacement'")
                    }
                }
            }
            
            // 检查中文+空格模式
            if (i > 0 && i < originalCodePoints.size - 1) {
                val prevChar = originalCodePoints[i - 1].toChar()
                val currentCodePoint = originalCodePoints[i]
                
                if (currentCodePoint == ' '.code && isChineseChar(prevChar)) {
                    val pattern = "$prevChar "
                    val replacement = prevChar.toString()
                    
                    if (success.contains(replacement)) {
                        changes[pattern] = replacement
                        // Log.d(TAG, "找到中文空格通用模式: '$pattern' -> '$replacement'")
                    }
                }
            }
            
            // 检查连续符号模式（只在中文字符后面）
            if (i > 0 && i < originalCodePoints.size - 1) {
                val prevChar = originalCodePoints[i - 1].toChar()
                val currentCodePoint = originalCodePoints[i]
                val nextChar = originalCodePoints[i + 1].toChar()
                
                if (isChineseChar(prevChar) && isSymbol(prevChar) && isSymbol(nextChar)) {
                    val pattern = "$prevChar${String(Character.toChars(currentCodePoint))}$nextChar"
                    val replacement = "$prevChar"
                    if (!success.contains(pattern) && success.contains(replacement)) {
                        changes["$currentCodePoint"] = ""
                        // Log.d(TAG, "找到中文后连续符号简化模式: '$pattern' -> '$replacement'")
                    }
                }
            }
            
            // 针对符号到中文的替换处理
            // 如果当前字符是符号，并且它在前面的分析中被识别为可能替换为中文
            val currentChar = String(Character.toChars(currentCodePoint))[0]
            if (isSymbol(currentChar) && symbolReplacements.containsKey(currentChar)) {
                val chineseChar = symbolReplacements[currentChar]
                if (chineseChar != null) {  // 添加非空检查
                    // 构建符号+中文的模式（/各），而不是单独的符号（/）
                    val pattern = "$currentChar$chineseChar"
                    val replacement = chineseChar.toString()
                    
                    changes[pattern] = replacement
                    // Log.d(TAG, "添加符号+中文模式: '$pattern' -> '$replacement'")
                    
                    // 防止学习单个符号到中文的错误映射
                    changes.remove(currentChar.toString()) // 删除可能存在的 / -> 各 映射
                }
            }
            
            i++
        }
        
        // 最后确保过滤掉任何单字符符号到中文的映射
        val filteredChanges = changes.filter { (from, to) -> 
            !(from.length == 1 && isSymbol(from[0]) && to.length == 1 && isChineseChar(to[0]))
        }.toMutableMap()
        
        // Log.d(TAG, "分析完成，找到的变化: $filteredChanges")
        return filteredChanges
    }
    
    private fun loadMappings() {
        try {
            appContext?.let { context ->
                val file = File(context.filesDir, MAPPINGS_FILE)
                if (file.exists()) {
                    val json = file.readText()
                    val type = object : TypeToken<Map<String, String>>() {}.type
                    mappings = Gson().fromJson<Map<String, String>>(json, type).toMutableMap()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "加载映射规则失败", e)
        }
    }
    
    private fun saveMappings() {
        try {
            appContext?.let { context ->
                val json = Gson().toJson(mappings)
                File(context.filesDir, MAPPINGS_FILE).writeText(json)
                
                context.getSharedPreferences("settings", Context.MODE_PRIVATE)
                    .edit()
                    .putString(LAST_LEARNED_KEY, lastLearnedRule)
                    .apply()
            }
        } catch (e: Exception) {
            Log.e(TAG, "保存失败", e)
        }
    }
    
    // 公共接口方法
    fun addNewRule(from: String, to: String) {
        if (from.isNotEmpty()) {
            mappings[from] = to
            lastLearnedRule = from
            updateSortedRules()
            saveMappings()
        }
    }
    
    fun updateRule(oldFrom: String, newFrom: String, newTo: String) {
        mappings.remove(oldFrom)
        mappings[newFrom] = newTo
        updateSortedRules()
        saveMappings()
    }
    
    fun deleteRule(from: String) {
        mappings.remove(from)
        if (from == lastLearnedRule) {
            lastLearnedRule = null
        }
        updateSortedRules()
        saveMappings()
    }
    
    fun getAllMappings() = LinkedHashMap<String, String>().apply {
        val entries = mappings.entries.toList()
        val lastLearnedIndex = entries.indexOfFirst { it.key == lastLearnedRule }
        
        if (lastLearnedIndex != -1) {
            put(entries[lastLearnedIndex].key, entries[lastLearnedIndex].value)
        }
        
        entries.filter { it.key != lastLearnedRule }
            .sortedBy { it.key }
            .forEach { put(it.key, it.value) }
    }
    
    fun exportRulesToString() = getAllMappings().entries.joinToString("\n") { "${it.key}→${it.value}" }
    
    fun parseRulesFromString(text: String) = text.split("\n")
        .filter { it.isNotEmpty() }
        .mapNotNull { line ->
            val parts = line.split("→")
            if (parts.size == 2) parts[0] to parts[1] else null
        }
    
    fun importRules(rules: List<Pair<String, String>>) {
        var hasNewMappings = false
        
        // 合并规则而不是覆盖
        rules.forEach { (from, to) ->
            if (from.isNotEmpty() && !mappings.containsKey(from)) {
                mappings[from] = to
                hasNewMappings = true
            }
        }
        
        if (hasNewMappings) {
            updateSortedRules()
            saveMappings()
        }
    }
    
    fun getLastLearnedRule() = lastLearnedRule
    
    // 记录原始输入
    fun recordOriginalInput(input: String) {
        // 这个方法应该调用 DataParser 的 recordOriginalInput
        DataParser.recordOriginalInput(input)
    }
}




