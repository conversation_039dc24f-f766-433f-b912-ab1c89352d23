package com.example.myshujuguanli.utils.parser

import com.example.myshujuguanli.utils.ZodiacUtils
import com.example.myshujuguanli.utils.SpecialBetResult

class LianXiaoBetParser {
    fun parseLianXiaoBet(
        count: Int,
        zodiacs: String,
        amount: Int,
        isComplex: Boolean = false
    ): List<SpecialBetResult> {
        val results = mutableListOf<SpecialBetResult>()
        val zodiacList = zodiacs.map { it.toString() }
        val zodiacMappings = ZodiacUtils.getZodiacMappings()
        
        if (zodiacList.size >= count) {
            if (isComplex) {
                // 处理复式连肖
                combinations(zodiacList.sorted(), count).forEach { combo ->
                    val numbers = combo.flatMap { zodiac -> 
                        zodiacMappings[zodiac] ?: emptyList()
                    }.sorted()
                    if (numbers.isNotEmpty()) {
                        results.add(SpecialBetResult(numbers, amount, "${count}连肖复式"))
                    }
                }
            } else {
                // 处理普通连肖
                val numbers = zodiacList.flatMap { zodiac -> 
                    zodiacMappings[zodiac] ?: emptyList()
                }.sorted()
                if (numbers.isNotEmpty()) {
                    results.add(SpecialBetResult(numbers, amount, "${count}连肖"))
                }
            }
        }
        
        return results
    }

    private fun <T> combinations(items: List<T>, count: Int): List<List<T>> {
        if (count == 0) return listOf(emptyList())
        if (items.isEmpty()) return emptyList()

        val result = mutableListOf<List<T>>()
        val first = items[0]
        val rest = items.drop(1)

        combinations(rest, count - 1).forEach { combo ->
            result.add(listOf(first) + combo)
        }
        result.addAll(combinations(rest, count))

        return result
    }
} 