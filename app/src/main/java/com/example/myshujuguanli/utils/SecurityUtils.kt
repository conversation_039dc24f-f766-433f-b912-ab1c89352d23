package com.example.myshujuguanli.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.os.SystemClock
import android.provider.Settings
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.io.BufferedReader
import java.io.InputStreamReader
import java.net.HttpURLConnection
import java.net.URL
import kotlin.math.abs

object SecurityUtils {
    private const val SERVER_URL = "https://giant-slime-turnover.glitch.me/api"
    const val PREFS_NAME = "security_prefs"
    const val KEY_DEVICE_STATUS = "device_status"
    const val KEY_DEVICE_ID = "device_id"
    const val KEY_EXPIRY_TIME = "expiry_time"
    private const val KEY_SERVICE_QQ = "service_qq"
    private const val KEY_SERVICE_WEIXIN = "service_weixin"
    private const val KEY_SERVICE_PHONE = "service_phone"
    private const val KEY_SERVICE_DESC = "service_desc"

    const val KEY_SERVER_TIME = "server_time"
    const val KEY_IS_ACTIVATED = "is_activated"
    private const val KEY_TIME_DESCRIPTION = "time_description"
    private const val KEY_LAST_KNOWN_TIME = "last_known_time"
    private const val TIME_TOLERANCE = 5 * 60 * 1000L // 5分钟时间容差
  
    private const val KEY_LAST_VALIDATION_TIME = "last_validation_time"
    private const val KEY_VALIDATION_FAIL_COUNT = "validation_fail_count"
    private const val MAX_VALIDATION_FAILS = 3

    // 新增常量
    const val KEY_SERVER_TIME_ELAPSED = "server_time_elapsed"

    // 状态信息数据类
    data class StatusInfo(
        val status: AppStatus,
        val deviceId: String,
        val expiryTime: Long,
        val serverTime: Long,
        val timeDescription: String,
        val serviceInfo: ServiceInfo
    )

    // AppStatus 枚举
    enum class AppStatus {
        NORMAL,         // 订阅状态（未到期）
        EXPIRED,        // 已过期
        ACTIVATED,      // 永久激活
        TRIAL          // 试用期
    }

    // 客服信息数据类
    data class ServiceInfo(
        val qq: String = "",
        val weixin: String = "",
        val phone: String = "",
        val description: String = ""
    )

    // 添加激活结果数据类 (用于兼容性)
    data class ActivationResult(
        val success: Boolean,
        val message: String? = null,
        val status: StatusInfo? = null
    )

    // 添加简化的激活方法 (用于兼容性)
    suspend fun activateApp(context: Context): ActivationResult {
        return withContext(Dispatchers.IO) {
            // 直接返回失败，因为我们已经移除了激活码功能
            ActivationResult(
                success = false,
                message = "激活码功能已被禁用",
                status = getCurrentLocalStatus(context)
            )
        }
    }

    // 检查应用状态
    suspend fun checkAppStatus(context: Context): StatusInfo {
        // 添加调试日志，输出详细的调用堆栈
        val stackTrace = Thread.currentThread().stackTrace
        val callStack = stackTrace.drop(1).take(8).joinToString("\n    ") { it.toString() }
        
        return withContext(Dispatchers.IO) {
            try {
                val currentTime = System.currentTimeMillis()
                
                val deviceId = getDeviceId(context)
                val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                
                // 先检查本地永久激活状态
                if (prefs.getBoolean(KEY_IS_ACTIVATED, false)) {
                    return@withContext getCurrentLocalStatus(context)
                }
                
                // 检查网络状态
                if (!isNetworkAvailable(context)) {
                    return@withContext getCurrentLocalStatus(context)
                }
                
                val response = makeApiRequest(
                    SERVER_URL,
                    JSONObject().apply {
                        put("deviceId", deviceId)
                        put("action", "check-expiry")
                        put("deviceInfo", getDeviceInfo())
                        put("timestamp", System.currentTimeMillis())
                    }
                )
                
                if (response.code == 200 || response.code == 403) {
                    val jsonResponse = JSONObject(response.body)
                    
                    // 验证服务器时间
                    val serverTime = jsonResponse.optLong("serverTime", System.currentTimeMillis())
                    if (!isValidTime(context, serverTime)) {

                        return@withContext StatusInfo(
                            status = AppStatus.EXPIRED,
                            deviceId = deviceId,
                            expiryTime = 0,
                            serverTime = serverTime,
                            timeDescription = "系统时间异常，请校准后重试",
                            serviceInfo = ServiceInfo()
                        )
                    }
                    
                    // 如果是已激活状态，确保本地状态同步
                    if (jsonResponse.optString("state") == "ACTIVATED") {
                        prefs.edit()
                            .putBoolean(KEY_IS_ACTIVATED, true)
                            .apply()
                    }
                    
                    parseAndSaveStatus(context, jsonResponse)
                } else {
                    getCurrentLocalStatus(context)
                }
            } catch (e: Exception) {
                getCurrentLocalStatus(context)
            }
        }
    }

    // 解析和保存状态
    private fun parseAndSaveStatus(context: Context, response: JSONObject): StatusInfo {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        
        // 从响应中获取所有数据
        val serverTime = response.optLong("serverTime", System.currentTimeMillis())
        val expiryTime = response.optLong("expiryTime", 0L)
        val deviceId = response.optString("deviceId", getDeviceId(context))
        val deviceState = response.optString("status", "EXPIRED")
        val isActivated = response.optBoolean("isActivated", false)
        
        // 保存服务器时间和对应的elapsedRealtime
        saveServerTime(context, serverTime)
        
        // 获取当前准确的服务器时间
        val currentServerTime = getCurrentServerTime(context)
        
        // 修改状态判断逻辑
        val status = when {
            isActivated -> AppStatus.ACTIVATED
            deviceState.equals("SUBSCRIBED", ignoreCase = true) -> AppStatus.NORMAL
            deviceState.equals("TRIAL", ignoreCase = true) -> AppStatus.TRIAL
            expiryTime > currentServerTime -> AppStatus.NORMAL // 使用当前服务器时间判断
            else -> AppStatus.EXPIRED
        }

        // 解析客服信息
        val serviceInfo = ServiceInfo(
            qq = response.optJSONObject("serviceInfo")?.optString("qq", "") ?: "",
            weixin = response.optJSONObject("serviceInfo")?.optString("weixin", "") ?: "",
            phone = response.optJSONObject("serviceInfo")?.optString("phone", "") ?: "",
            description = response.optJSONObject("serviceInfo")?.optString("description", "") ?: ""
        )

        // 使用当前服务器时间计算剩余时间描述
        val timeDescription = response.optString("timeDescription", "") ?: when (status) {
            AppStatus.ACTIVATED -> "永久版"
            AppStatus.TRIAL,
            AppStatus.NORMAL -> {
                val remainingTime = expiryTime - currentServerTime // 使用当前服务器时间计算
                if (remainingTime > 0) {
                    val days = remainingTime / (24 * 60 * 60 * 1000)
                    val hours = (remainingTime % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000)
                    "${days}天${hours}小时"
                } else {
                    "已过期"
                }
            }
            AppStatus.EXPIRED -> "已过期"
        }

        // 保存状态到本地
        prefs.edit().apply {
            putString(KEY_DEVICE_STATUS, status.name)
            putString(KEY_DEVICE_ID, deviceId)
            putLong(KEY_EXPIRY_TIME, expiryTime)
            putBoolean(KEY_IS_ACTIVATED, status == AppStatus.ACTIVATED)
            putString(KEY_TIME_DESCRIPTION, timeDescription)
        }.commit()

        return StatusInfo(
            status = status,
            deviceId = deviceId,
            expiryTime = expiryTime,
            serverTime = currentServerTime, // 返回当前服务器时间
            timeDescription = timeDescription,
            serviceInfo = serviceInfo
        )
    }

    // 获取本地状态（离线时使用）
    fun getCurrentLocalStatus(context: Context): StatusInfo {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val currentServerTime = getCurrentServerTime(context)
        val deviceStatus = prefs.getString(KEY_DEVICE_STATUS, null)
        
        // 如果本地状态是永久激活，直接返回激活状态
        if (deviceStatus == AppStatus.ACTIVATED.name) {
            return StatusInfo(
                status = AppStatus.ACTIVATED,
                deviceId = prefs.getString(KEY_DEVICE_ID, getDeviceId(context)) ?: getDeviceId(context),
                expiryTime = 0L,
                serverTime = currentServerTime,
                timeDescription = "永久版",
                serviceInfo = ServiceInfo(
                    qq = prefs.getString(KEY_SERVICE_QQ, "") ?: "",
                    weixin = prefs.getString(KEY_SERVICE_WEIXIN, "") ?: "",
                    phone = prefs.getString(KEY_SERVICE_PHONE, "") ?: "",
                    description = prefs.getString(KEY_SERVICE_DESC, "") ?: ""
                )
            )
        }

        // 获取存储的过期时间
        val storedExpiryTime = prefs.getLong(KEY_EXPIRY_TIME, 0L)
        
        // 使用当前服务器时间判断状态
        val status = when {
            deviceStatus == AppStatus.TRIAL.name -> AppStatus.TRIAL
            storedExpiryTime > currentServerTime -> AppStatus.NORMAL
            else -> AppStatus.EXPIRED
        }

        // 从本地获取客服信息
        val serviceInfo = ServiceInfo(
            qq = prefs.getString(KEY_SERVICE_QQ, "") ?: "",
            weixin = prefs.getString(KEY_SERVICE_WEIXIN, "") ?: "",
            phone = prefs.getString(KEY_SERVICE_PHONE, "") ?: "",
            description = prefs.getString(KEY_SERVICE_DESC, "") ?: ""
        )

        // 计算剩余时间描述
        val timeDescription = when (status) {
            AppStatus.ACTIVATED -> "永久版"
            AppStatus.TRIAL -> "试用期"
            AppStatus.NORMAL -> {
                val remainingTime = storedExpiryTime - currentServerTime // 使用当前服务器时间计算
                if (remainingTime > 0) {
                    val days = remainingTime / (24 * 60 * 60 * 1000)
                    val hours = (remainingTime % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000)
                    "${days}天${hours}小时"
                } else {
                    "已过期"
                }
            }
            AppStatus.EXPIRED -> "已过期"
        }

        return StatusInfo(
            status = status,
            deviceId = prefs.getString(KEY_DEVICE_ID, getDeviceId(context)) ?: getDeviceId(context),
            expiryTime = storedExpiryTime,
            serverTime = currentServerTime,
            timeDescription = timeDescription,
            serviceInfo = serviceInfo
        )
    }

    // 修改网络请求工具方法
    private suspend fun makeApiRequest(
        url: String,
        requestBody: JSONObject? = null,
        retryCount: Int = 2
    ): ApiResponse {
        var lastException: Exception? = null
        
        for (attempt in 0 until retryCount) {
            try {             
                val connection = withContext(Dispatchers.IO) {
                    URL(url).openConnection()
                } as HttpURLConnection
                connection.apply {
                    connectTimeout = 5000  // 5秒
                    readTimeout = 5000     // 5秒
                    requestMethod = if (requestBody != null) "POST" else "GET"
                    setRequestProperty("Content-Type", "application/json")
                    setRequestProperty("Accept", "application/json")
                    setRequestProperty("User-Agent", "MyShujuguanli/1.0")
                    doInput = true
                    
                    if (requestBody != null) {
                        doOutput = true
                        outputStream.use {
                            it.write(requestBody.toString().toByteArray())
                        }
                    }
                }

                val responseCode = connection.responseCode
                // Log.d("SecurityUtils", "响应码: $responseCode")

                val responseBody = when (responseCode) {
                    HttpURLConnection.HTTP_OK, 
                    HttpURLConnection.HTTP_BAD_REQUEST, 
                    HttpURLConnection.HTTP_UNAUTHORIZED,
                    HttpURLConnection.HTTP_FORBIDDEN -> {
                        connection.inputStream.use { stream ->
                            BufferedReader(InputStreamReader(stream)).use { reader ->
                                reader.readText()
                            }
                        }
                    }
                    else -> {
                        connection.errorStream?.use { stream ->
                            BufferedReader(InputStreamReader(stream)).use { reader ->
                                reader.readText()
                            }
                        } ?: ""
                    }
                }
                
                // Log.d("SecurityUtils", "响应体: $responseBody")

                // 检查响应是否为JSON
                if (responseBody.isNotEmpty()) {
                    try {
                        JSONObject(responseBody) // 验证JSON格式
                        return ApiResponse(responseCode, responseBody)
                    } catch (e: Exception) {
                        throw Exception("Invalid JSON response: $responseBody")
                    }
                } else {
                    throw Exception("Empty response")
                }
            } catch (e: Exception) {
                lastException = e
                if (attempt < retryCount - 1) {
                    delay(500L * (attempt + 1)) // 500ms
                }
            }
        }
        
        throw lastException ?: Exception("网络请求失败")
    }

    // 获取设备信息
    private fun getDeviceInfo(): String {
        return JSONObject().apply {
            put("BOARD", Build.BOARD)
            put("BRAND", Build.BRAND)
            put("DEVICE", Build.DEVICE)
            put("MANUFACTURER", Build.MANUFACTURER)
            put("MODEL", Build.MODEL)
            put("PRODUCT", Build.PRODUCT)
        }.toString()
    }


    private data class ApiResponse(
        val code: Int,
        val body: String
    )

    // 获取设备ID
    fun getDeviceId(context: Context): String {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val savedDeviceId = prefs.getString(KEY_DEVICE_ID, null)
        
        return if (savedDeviceId != null) {
            savedDeviceId
        } else {
            // 尝试获取序列号
            val serialNo = try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    Build.getSerial()
                } else {
                    @Suppress("DEPRECATION")
                    Build.SERIAL
                }
            } catch (e: Exception) {
                // 如果获取序列号失败，使用 ANDROID_ID
                Settings.Secure.getString(
                    context.contentResolver,
                    Settings.Secure.ANDROID_ID
                )
            }
            
            // 保存设备ID
            prefs.edit().putString(KEY_DEVICE_ID, serialNo).apply()
            // Log.d("SecurityUtils", "生成新的设备ID: $serialNo")
            
            serialNo
        }
    }
    // 添加网络检查工具
    private fun isNetworkAvailable(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork
        val capabilities = connectivityManager.getNetworkCapabilities(network)
        return capabilities != null && (
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)
        )
    }

    // 时间验证
    private fun isValidTime(context: Context, serverTime: Long): Boolean {
        val currentTime = System.currentTimeMillis()
        val lastKnownTime = getLastKnownTime(context)
        
        // 检查系统时间是否被回调
        if (currentTime < lastKnownTime) {
            // Log.w("TimeValidator", "检测到系统时间可能被修改: current=$currentTime, last=$lastKnownTime")
            return false
        }
        
        // 检查与服务器时间的偏差
        val timeDiff = abs(currentTime - serverTime)
        if (timeDiff > TIME_TOLERANCE) {
            // Log.w("TimeValidator", "检测到系统时间偏差过大: diff=$timeDiff")
            return false
        }
        
        // 更新最后已知时间
        saveLastKnownTime(context, currentTime)
        return true
    }

    private fun getLastKnownTime(context: Context): Long {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            .getLong(KEY_LAST_KNOWN_TIME, 0L)
    }

    private fun saveLastKnownTime(context: Context, time: Long) {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            .edit()
            .putLong(KEY_LAST_KNOWN_TIME, time)
            .apply()
    }


    
    // 执行本地验证
    fun validateLocalStatus(context: Context): Boolean {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val currentTime = System.currentTimeMillis()
        
        // 检查验证失败次数
        val failCount = prefs.getInt(KEY_VALIDATION_FAIL_COUNT, 0)
        if (failCount >= MAX_VALIDATION_FAILS) {
            return false
        }
        
        val validationResult = performValidation(context, currentTime)
        
        if (!validationResult) {
            // 增加失败计数
            prefs.edit()
                .putInt(KEY_VALIDATION_FAIL_COUNT, failCount + 1)
                .apply()
        } else {
            // 重置失败计数
            prefs.edit()
                .putInt(KEY_VALIDATION_FAIL_COUNT, 0)
                .apply()
        }
        
        return validationResult
    }
    
    private fun performValidation(context: Context, currentTime: Long): Boolean {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val expiryTime = prefs.getLong(KEY_EXPIRY_TIME, 0)
        val isActivated = prefs.getBoolean(KEY_IS_ACTIVATED, false)
        val lastKnownTime = prefs.getLong(KEY_LAST_KNOWN_TIME, currentTime)
        
        // 获取当前服务器时间
        val currentServerTime = getCurrentServerTime(context)
        
        // 更新最后验证时间
        prefs.edit().putLong(KEY_LAST_VALIDATION_TIME, currentTime).apply()
        
        // 多重验证
        return when {
            currentTime < lastKnownTime -> false // 时间被回调
            isActivated -> true // 永久激活
            expiryTime <= currentServerTime -> false // 使用服务器时间判断是否过期
            expiryTime - currentServerTime > 365L * 24 * 60 * 60 * 1000 -> false // 使用服务器时间判断可疑的过期时间
            else -> true
        }
    }

    // 获取当前服务器时间
    fun getCurrentServerTime(context: Context): Long {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val serverTime = prefs.getLong(KEY_SERVER_TIME, 0)
        val serverTimeElapsed = prefs.getLong(KEY_SERVER_TIME_ELAPSED, 0)
        val currentElapsed = SystemClock.elapsedRealtime()
        
        // Log.d("SecurityUtils", """
        //     getCurrentServerTime 详情:
        //     保存的服务器时间: $serverTime (${java.util.Date(serverTime)})
        //     保存时的elapsed: $serverTimeElapsed
        //     当前elapsed: $currentElapsed
        //     elapsed差值: ${currentElapsed - serverTimeElapsed}
        // """.trimIndent())
        
        // 如果没有保存的服务器时间，使用系统时间
        if (serverTime == 0L || serverTimeElapsed == 0L) {
            // Log.d("SecurityUtils", "没有保存的服务器时间，使用系统时间: ${System.currentTimeMillis()}")
            return System.currentTimeMillis()
        }
        
        // 计算从获取服务器时间到现在流逝的时间
        val elapsedTimeDiff = currentElapsed - serverTimeElapsed
        
        // 计算当前服务器时间
        val calculatedServerTime = serverTime + elapsedTimeDiff
        
        // Log.d("SecurityUtils", """
        //     时间计算结果:
        //     计算的服务器时间: $calculatedServerTime (${java.util.Date(calculatedServerTime)})
        //     流逝时间: ${elapsedTimeDiff/1000}秒
        //     系统当前时间: ${System.currentTimeMillis()} (${java.util.Date(System.currentTimeMillis())})
        // """.trimIndent())
        
        return calculatedServerTime
    }

    // 保存服务器时间和对应的elapsedRealtime
    private fun saveServerTime(context: Context, serverTime: Long) {
        val currentElapsed = SystemClock.elapsedRealtime()
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        
        // 获取之前保存的服务器时间
        val lastServerTime = prefs.getLong(KEY_SERVER_TIME, 0)
        
        // 如果是首次保存或服务器时间有更新，则重置elapsed时间
        if (lastServerTime == 0L || lastServerTime != serverTime) {
            // Log.d("SecurityUtils", "服务器时间已更新，重置elapsed时间")
            prefs.edit()
                .putLong(KEY_SERVER_TIME, serverTime)
                .putLong(KEY_SERVER_TIME_ELAPSED, currentElapsed)
                .apply()
            
            // Log.d("SecurityUtils", "更新服务器时间: $serverTime, 重置elapsed: $currentElapsed")
        } else {
            // Log.d("SecurityUtils", "服务器时间未变化，保持原有elapsed")
        }
    }
}
