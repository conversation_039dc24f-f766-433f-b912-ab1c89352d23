const express = require("express");
const crypto = require("crypto");
const bodyParser = require("body-parser");
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const schedule = require('node-schedule');

const app = express();
const port = process.env.PORT || 3000;

// 创建 SQLite 数据库连接
const db = new sqlite3.Database(path.join(__dirname, 'activation.db'), (err) => {
    if (err) {
        console.error('数据库连接错误:', err);
    } else {
        console.log('已连接到 SQLite 数据库');
        // 只在表不存在时创建表
        db.run(`
            CREATE TABLE IF NOT EXISTS devices (
                deviceId TEXT PRIMARY KEY,
                firstInstallTime INTEGER,
                trialEndTime INTEGER,
                canTrial INTEGER,
                hasUsedTrial INTEGER,
                isActivated INTEGER, 
                activationCode TEXT,
                activationTime INTEGER,
                deviceInfo TEXT,
                lastCheckTime INTEGER,
                createdAt INTEGER,
                updatedAt INTEGER
            )
        `, (err) => {
            if (err) {
                console.error('创建表失败:', err);
            } else {
                console.log('数据库表检查/创建成功');
            }
        });
    }
});

// 中间件配置
app.use((req, res, next) => {
    res.header("Access-Control-Allow-Origin", "*");
    res.header("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
    res.header("Access-Control-Allow-Headers", "Content-Type");
    next();
});
app.use(bodyParser.json());

// 生成激活码
function generateActivationCode(deviceId) {
    const key = process.env.ACTIVATION_KEY || "your_secret_key_123";
    const input = `${deviceId}${key}`;
    return crypto
        .createHash("sha256")
        .update(input)
        .digest("hex")
        .substring(0, 16);
}

// 设备初始化
app.post("/api/device/initialize", async (req, res) => {
    console.log("收到初始化请求:", req.body);
    const { deviceId, deviceInfo } = req.body;
    const now = Date.now();
    const trialDuration = 3 * 24 * 60 * 60 * 1000; // 3天试用期

    try {
        // 检查数据库连接
        if (!db) {
            console.error("数据库连接未初始化");
            throw new Error("数据库连接失败");
        }

        // 检查设备是否已存在
        console.log("查询设备:", deviceId);
        const device = await new Promise((resolve, reject) => {
            db.get("SELECT * FROM devices WHERE deviceId = ?", [deviceId], (err, row) => {
                if (err) {
                    console.error("查询设备错误:", err);
                    reject(err);
                } else {
                    console.log("查询结果:", row);
                    resolve(row);
                }
            });
        });

        if (device) {
            console.log("\n=== 设备状态检查 ===");
            console.log("设备已存在，完整设备信息:", device);
            if (device.isActivated === 1) {
                console.log("设备已激活，准备返回激活信息");
                try {
                    const deviceInfo = JSON.parse(device.deviceInfo);
                    console.log("解析的设备信息:", deviceInfo);
                    const response = {
                        success: true,
                        status: "ACTIVATED",
                        activationInfo: {
                            deviceId: device.deviceId,
                            activationCode: device.activationCode,
                            activationTime: device.activationTime,
                            deviceInfo: deviceInfo
                        },
                        message: "把激活信息发到手机上并永久保存"
                    };
                    console.log("返回数据:", response);
                    console.log("=== 状态检查完成 ===\n");
                    return res.json(response);
                } catch (e) {
                    console.error("解析设备信息失败:", e);
                    throw e;
                }
            } else if (device.canTrial === 1) {
                if (now < device.trialEndTime) {
                    return res.json({
                        success: true,
                        status: "TRIAL",
                        trialEndTime: device.trialEndTime,
                        message: "带着剩余试用时间进入软件试用"
                    });
                } else {
                    return res.json({
                        success: true,
                        status: "EXPIRED",
                        message: "停留在激活界面"
                    });
                }
            }
        }

        // 新设备初始化
        console.log("初始化新设备，进入试用期");
        const deviceData = {
            deviceId,
            firstInstallTime: now,
            trialEndTime: now + trialDuration,
            canTrial: 1,
            hasUsedTrial: 0,
            isActivated: 0,
            deviceInfo: JSON.stringify(deviceInfo),
            lastCheckTime: now,
            createdAt: now,
            updatedAt: now
        };

        await new Promise((resolve, reject) => {
            const sql = `
                INSERT INTO devices (
                    deviceId, firstInstallTime, trialEndTime, canTrial, 
                    hasUsedTrial, isActivated, deviceInfo, lastCheckTime,
                    createdAt, updatedAt
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;
            
            db.run(sql, [
                deviceData.deviceId,
                deviceData.firstInstallTime,
                deviceData.trialEndTime,
                deviceData.canTrial,
                deviceData.hasUsedTrial,
                deviceData.isActivated,
                deviceData.deviceInfo,
                deviceData.lastCheckTime,
                deviceData.createdAt,
                deviceData.updatedAt
            ], function(err) {
                if (err) {
                    console.error("插入数据错误:", err);
                    reject(err);
                } else {
                    console.log("插入成功，lastID:", this.lastID);
                    resolve();
                }
            });
        });

        console.log("初始化成功，返回试用状态");
        res.json({
            success: true,
            status: "TRIAL",
            trialEndTime: deviceData.trialEndTime,
            message: "生成试用信息并发送到手机上"
        });

    } catch (error) {
        console.error("设备初始化错误:", error);
        res.status(500).json({
            success: false,
            error: "SERVER_ERROR",
            message: error.message || "设备初始化失败"
        });
    }
});

// 设备状态检查
app.post("/api/device/status", async (req, res) => {
    console.log("收到设备状态检查请求:", req.body);
    const { deviceId } = req.body;
    const now = Date.now();

    try {
        // 检查设备是否存在
        const device = await new Promise((resolve, reject) => {
            db.get("SELECT * FROM devices WHERE deviceId = ?", [deviceId], (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });

        // 如果设备已激活
        if (device && device.isActivated === 1) {
            console.log("设备已激活");
            return res.json({
                status: "ACTIVATED",
                message: "正常进入软件使用"
            });
        }

        // 检查服务器激活状态
        if (!device) {
            // 新设备，进入试用状态
            console.log("新设备，进入试用状态");
            const trialEndTime = now + (3 * 24 * 60 * 60 * 1000); // 3天试用期
            
            await new Promise((resolve, reject) => {
                db.run(`
                    INSERT INTO devices (
                        deviceId, firstInstallTime, trialEndTime, 
                        canTrial, hasUsedTrial, isActivated
                    ) VALUES (?, ?, ?, 1, 0, 0)
                `, [deviceId, now, trialEndTime], (err) => {
                    if (err) reject(err);
                    else resolve();
                });
            });

            return res.json({
                status: "TRIAL",
                trialEndTime,
                message: "生成试用信息并发送到手机上"
            });
        }

        // 检查试用状态
        if (device.canTrial === 1) {
            if (now < device.trialEndTime) {
                console.log("试用期内");
                return res.json({
                    status: "TRIAL",
                    trialEndTime: device.trialEndTime,
                    message: "带着剩余试用时间进入软件试用"
                });
            } else {
                console.log("试用期已结束");
                return res.json({
                    status: "EXPIRED",
                    message: "停留在激活界面"
                });
            }
        }

        // 其他情况，需要激活
        return res.json({
            status: "NEED_ACTIVATION",
            message: "用户点击试用"
        });

    } catch (error) {
        console.error("状态检查错误:", error);
        res.status(500).json({
            status: "ERROR",
            message: "服务器错误，请稍后重试"
        });
    }
});

// 修改激活接口
app.post("/api/activate", async (req, res) => {
    console.log("\n=== 激活流程开始 ===");
    console.log("收到激活请求:", req.body);
    const { deviceId, activationCode, deviceInfo } = req.body;
    const now = Date.now();

    try {
        // 验证激活码
        console.log("正在验证激活码...");
        const expectedCode = generateActivationCode(deviceId);
        console.log("期望的激活码:", expectedCode);
        console.log("收到的激活码:", activationCode);
        
        if (activationCode.toLowerCase() !== expectedCode.toLowerCase()) {
            console.log("激活码验证失败");
            return res.status(400).json({
                success: false,
                message: "激活码无效"
            });
        }
        console.log("激活码验证成功");

        // 更新设备状态，保存完整的激活信息
        console.log("正在更新设备状态...");
        await new Promise((resolve, reject) => {
            const sql = `
                UPDATE devices SET
                    isActivated = 1,
                    activationCode = ?,
                    activationTime = ?,
                    deviceInfo = ?,
                    canTrial = 0,
                    hasUsedTrial = 1,
                    lastCheckTime = ?,
                    updatedAt = ?
                WHERE deviceId = ?
            `;
            console.log("执行SQL:", sql);
            console.log("SQL参数:", [
                activationCode,
                now,
                JSON.stringify(deviceInfo),
                now,
                now,
                deviceId
            ]);
            
            db.run(sql, [
                activationCode,
                now,
                JSON.stringify(deviceInfo),
                now,
                now,
                deviceId
            ], function(err) {
                if (err) {
                    console.error("更新设备状态失败:", err);
                    reject(err);
                } else {
                    console.log("更新成功, changes:", this.changes);
                    resolve();
                }
            });
        });

        // 验证更新是否成功
        console.log("验证更新结果...");
        const updatedDevice = await new Promise((resolve, reject) => {
            db.get("SELECT * FROM devices WHERE deviceId = ?", [deviceId], (err, row) => {
                if (err) {
                    console.error("验证查询失败:", err);
                    reject(err);
                } else {
                    console.log("更新后的设备数据:", row);
                    resolve(row);
                }
            });
        });

        if (!updatedDevice || updatedDevice.isActivated !== 1) {
            throw new Error("设备状态更新失败");
        }

        // 返回完整的激活信息
        console.log("准备返回激活信息...");
        const response = {
            success: true,
            status: "ACTIVATED",
            activationInfo: {
                deviceId,
                activationCode,
                activationTime: now,
                deviceInfo
            },
            message: "把激活信息发到手机上并永久保存"
        };
        console.log("返回数据:", response);
        res.json(response);
        console.log("=== 激活流程完成 ===\n");

    } catch (error) {
        console.error("激活过程出错:", error);
        res.status(500).json({
            success: false,
            message: "激活失败，请稍后重试"
        });
    }
});

// 试用状态检查
app.post("/api/trial/check", (req, res) => {
    console.log("收到试用检查请求:", req.body);
    const { deviceId } = req.body;

    db.get("SELECT * FROM devices WHERE deviceId = ?", [deviceId], (err, device) => {
        if (err) {
            console.error("Error:", err);
            return res.status(500).json({ success: false, error: "Server error" });
        }

        if (!device) {
            res.json({
                canTrial: true,
                hasUsedTrial: false,
                trialEndTime: Date.now() + 3 * 24 * 60 * 60 * 1000,
            });
            return;
        }

        if (device.isActivated) {
            res.json({
                canTrial: false,
                isActivated: true,
                activationCode: device.activationCode,
            });
        } else {
            const now = Date.now();
            res.json({
                canTrial: device.canTrial && now < device.trialEndTime,
                hasUsedTrial: device.hasUsedTrial,
                trialEndTime: device.trialEndTime,
            });
        }
    });
});

// 修改重置数据库的端点，支持 GET 和 POST
app.get("/api/reset-database", handleReset);  // 添加 GET 支持
app.post("/api/reset-database", handleReset); // 保留 POST 支持

// 将重置逻辑抽取为单独的处理函数
async function handleReset(req, res) {
    console.log("\n=== 开始重置数据库 ===");
    try {
        await new Promise((resolve, reject) => {
            // 先删除表
            db.run("DROP TABLE IF EXISTS devices", (err) => {
                if (err) {
                    console.error("删除表失败:", err);
                    reject(err);
                } else {
                    console.log("成功删除表");
                    // 重新创建表
                    db.run(`
                        CREATE TABLE devices (
                            deviceId TEXT PRIMARY KEY,
                            firstInstallTime INTEGER,
                            trialEndTime INTEGER,
                            canTrial INTEGER,
                            hasUsedTrial INTEGER,
                            isActivated INTEGER, 
                            activationCode TEXT,
                            activationTime INTEGER,
                            deviceInfo TEXT,
                            lastCheckTime INTEGER,
                            createdAt INTEGER,
                            updatedAt INTEGER
                        )
                    `, (err) => {
                        if (err) {
                            console.error("重建表失败:", err);
                            reject(err);
                        } else {
                            console.log("成功重建表");
                            resolve();
                        }
                    });
                }
            });
        });

        console.log("=== 数据库重置完成 ===\n");
        res.json({
            success: true,
            message: "数据库已成功重置"
        });
    } catch (error) {
        console.error("重置数据库时出错:", error);
        res.status(500).json({
            success: false,
            message: "重置数据库失败"
        });
    }
}

// 错误处理中间件
app.use((err, req, res, next) => {
    console.error("服务器错误:", err);
    res.status(500).json({
        success: false,
        error: "SERVER_ERROR",
        message: "服务器内部错误"
    });
});

// 删除命令行相关代码，替换为定时任务
schedule.scheduleJob('0 8 * * *', async () => {
    console.log('\n=== 每日设备状态报告 ===');
    console.log('时间:', new Date().toLocaleString());
    
    try {
        const devices = await new Promise((resolve, reject) => {
            db.all('SELECT * FROM devices', [], (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });
        
        console.log('设备总数:', devices.length);
        console.log('设备详细信息:');
        devices.forEach((device, index) => {
            console.log(`\n设备 ${index + 1}:`);
            console.log('设备ID:', device.deviceId);
            console.log('激活状态:', device.isActivated ? '已激活' : '未激活');
            console.log('试用状态:', device.canTrial ? '可试用' : '不可试用');
            console.log('首次安装时间:', new Date(device.firstInstallTime).toLocaleString());
            console.log('最后检查时间:', new Date(device.lastCheckTime).toLocaleString());
        });
        console.log('\n=== 报告结束 ===\n');
    } catch (error) {
        console.error('生成设备报告时出错:', error);
    }
});

// 启动服务器
app.listen(port, () => {
    console.log(`服务器运行在端口 ${port}`);
});

// 程序退出时关闭数据库连接
process.on('SIGINT', () => {
    db.close(() => {
        console.log('数据库连接已关闭');
        process.exit(0);
    });
});
