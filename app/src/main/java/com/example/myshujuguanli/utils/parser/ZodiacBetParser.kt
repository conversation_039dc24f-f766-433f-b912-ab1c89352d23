package com.example.myshujuguanli.utils.parser

import com.example.myshujuguanli.utils.ZodiacUtils
import com.example.myshujuguanli.utils.SpecialBetResult

class ZodiacBetParser {
    fun parse(
        zodiacs: String,
        amount: Int,
        betType: String,
        isComplex: Boolean = false
    ): List<SpecialBetResult> {
        val results = mutableListOf<SpecialBetResult>()
        
        if (isComplex) {
            zodiacs.forEach { zodiac ->
                val numbers: List<Int> = ZodiacUtils.getZodiacMappings()[zodiac.toString()] ?: listOf()
                if (numbers.isNotEmpty()) {
                    results.add(SpecialBetResult(numbers, amount, betType))
                }
            }
        } else {
            val numbers: List<Int> = ZodiacUtils.getZodiacMappings()[zodiacs] ?: listOf()
            if (numbers.isNotEmpty()) {
                results.add(SpecialBetResult(numbers, amount, betType))
            }
        }
        
        return results
    }
} 