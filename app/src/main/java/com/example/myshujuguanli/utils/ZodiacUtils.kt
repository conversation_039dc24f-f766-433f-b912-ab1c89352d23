package com.example.myshujuguanli.utils

import android.content.Context
import org.json.JSONArray
import org.json.JSONObject

object ZodiacUtils {
    private const val PREFS_NAME = "zodiac_prefs"
    private const val KEY_ZODIAC_MAPPINGS = "zodiac_mappings"
    private const val KEY_BASE_YEAR = "base_year"

    // 生肖顺序（按照农历年份排序）
    private val ZODIAC_ORDER = listOf("鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪")

    private var customZodiacMap = mutableMapOf<String, List<Int>>()

    // 获取当前使用的生肖映射
    fun getZodiacMappings(): Map<String, List<Int>> {
        return customZodiacMap
    }

    // 获取当前基准年份
    fun getCurrentBaseYear(context: Context): Int {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            .getInt(KEY_BASE_YEAR, 2024) // 2024 作为默认值
    }

    // 更新年份并重新生成映射
    fun updateBaseYear(context: Context, year: Int) {
        // 保存年份
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            .edit()
            .putInt(KEY_BASE_YEAR, year)
            .apply()
        
        // 生成并保存新的映射
        val newMapping = generateZodiacMapping(year)
        updateZodiacMappings(context, newMapping)
    }

    // 更新生肖映射
    fun updateZodiacMappings(context: Context, newMappings: Map<String, List<Int>>) {
        customZodiacMap.clear()
        customZodiacMap.putAll(newMappings)
        saveToPreferences(context, newMappings)
    }

    // 加载保存的映射
    fun loadSavedMappings(context: Context) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val savedMappings = prefs.getString(KEY_ZODIAC_MAPPINGS, null)
        if (savedMappings != null) {
            try {
                customZodiacMap = jsonToMap(savedMappings).toMutableMap()
            } catch (e: Exception) {
                e.printStackTrace()
                // 如果读取失败，使用当前年份生成新的映射
                customZodiacMap = generateZodiacMapping(getCurrentBaseYear(context)).toMutableMap()
            }
        } else {
            // 如果没有保存的映射，使用当前年份生成新的映射
            customZodiacMap = generateZodiacMapping(getCurrentBaseYear(context)).toMutableMap()
        }
    }

    // 根据年份生成生肖映射
    fun generateZodiacMapping(year: Int): Map<String, List<Int>> {
        // 计算当年生肖在序列中的位置
        val yearZodiacIndex = (year - 1900) % 12
        val result = LinkedHashMap<String, List<Int>>() // 使用 LinkedHashMap 保持插入顺序
        
        // 先添加固定顺序的生肖
        ZODIAC_ORDER.forEach { zodiac ->
            result[zodiac] = emptyList()
        }
        
        // 为每个生肖生成对应的号码
        ZODIAC_ORDER.forEachIndexed { index, zodiac ->
            val numbers = when {
                index == yearZodiacIndex -> {
                    // 当年生肖
                    listOf(1, 13, 25, 37, 49).filter { it <= 49 }
                }
                index == (yearZodiacIndex + 1) % 12 -> {
                    // 下一年生肖
                    listOf(12, 24, 36, 48).filter { it <= 49 }
                }
                else -> {
                    // 计算其他生肖的起始数字
                    val distance = (index - yearZodiacIndex + 12) % 12
                    val startNum = if (distance > 1) {
                        13 - distance
                    } else {
                        2  // 当年生肖的前一个生肖从2开始
                    }
                    listOf(startNum, startNum + 12, startNum + 24, startNum + 36)
                        .filter { it in 1..49 }
                }
            }
            result[zodiac] = numbers
        }
        
        return result
    }

    // 获取指定年份的生肖
    fun getZodiacForYear(year: Int): String {
        val index = (year - 1900) % 12
        return ZODIAC_ORDER[index]
    }

    // 保存到 SharedPreferences
    private fun saveToPreferences(context: Context, mappings: Map<String, List<Int>>) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit().putString(KEY_ZODIAC_MAPPINGS, mapToJson(mappings)).apply()
    }

    // 转换为 JSON
    private fun mapToJson(map: Map<String, List<Int>>): String {
        return JSONObject().apply {
            map.forEach { (zodiac, numbers) ->
                put(zodiac, JSONArray(numbers))
            }
        }.toString()
    }

    // 从 JSON 转换
    private fun jsonToMap(json: String): Map<String, List<Int>> {
        val result = mutableMapOf<String, List<Int>>()
        val jsonObj = JSONObject(json)
        jsonObj.keys().forEach { zodiac ->
            val numbers = mutableListOf<Int>()
            val jsonArray = jsonObj.getJSONArray(zodiac)
            for (i in 0 until jsonArray.length()) {
                numbers.add(jsonArray.getInt(i))
            }
            result[zodiac] = numbers
        }
        return result
    }

    // 获取号码对应的生肖
    fun getZodiacForNumber(number: Int): String? {
        return customZodiacMap.entries.find { (_, numbers) -> 
            numbers.contains(number) 
        }?.key
    }
} 