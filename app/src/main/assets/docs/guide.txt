# 特码类标准输入格式
## 金额格式说明
- [color=#FF0000]支持中文数字金额（如"十"、"一百"、"一百五十"等）[/color]
- [color=#FF0000]金额必须带单位，如"10元"、"十元"、"一百五十元"[/color]
- [color=#FF0000]"各"字前后不能有分隔符号(各。10元 这羊的解析不了)[/color]

## 输入格式示例
### 纯数字格式:
- 01.02.03.04.05各10元
  01.02.03.04.05各十元 (支持数字或者中文数字金额)
### 生肖格式:
- 鼠牛马各10元
  鼠.龙.马各一百元 (生肖格式可有可无分隔符合)
### 生肖+数字混合格式:
- 鼠牛马.01.02.03各十元
  01.02.03.鼠.牛.马各100元 (混合格式需要分隔符合)
### 头数格式:
- 2头各10元
  01234头各十元 (支持单个或者多个头数一起写,不需要分隔符合)
### 尾数格式:
- 2尾各10元 
  0123456789尾各十元 (支持单个或者多个尾数一起写,不需要分隔符合)
### 范围格式:
- 1到30各10元
  10至30各五十元 (“到”和”至” 是关键字)
### 大小双单格式:
- 大各10元
  小双各十元 
  小数单各10元 
  大数单数各50元 (“数”可有可无)
### 波色格式:
- 红各10元 
  红波各十元
  红单各10元 
  红波单数各十元 
  红蓝各10元 (“波”和”数”可有可无)

## 一些常见的错误和修正
### 不标准的货币单位:
- 原: 23=[color=#FF0000]50#[/color]43.39.各[color=#FF0000]20#[/color]36=[color=#FF0000]10#[/color]15.25.14.22.02.44各[color=#FF0000]5#[/color] ❌ 
- 改: 23=[color=#FF0000]50元#[/color]43.39.各[color=#FF0000]20元#[/color]36=[color=#FF0000]10元#[/color]15.25.14.22.02.44各[color=#FF0000]5元#[/color] ✅

- 原: 05=[color=#FF0000]150[/color]，10=[color=#FF0000]1000[/color] ❌ [color=#FF0000]缺少货币单位[/color]   
- 改: 05=[color=#FF0000]150元[/color]，10=[color=#FF0000]1000元[/color] ✅

- 原: 05=10=[color=#FF0000]150[/color]，10=18=[color=#FF0000]1000[/color] ❌ [color=#FF0000]两个号码以上需要各,缺少货币单位[/color]  
- 原: 05=10=[color=#FF0000]各150元[/color]，10=18=[color=#FF0000]各1000元[/color] ✅

- 01.02.03各[color=#FF0000]两百[/color]  ❌ 金额要用标准写法 
  01.02.03各[color=#FF0000]二百元[/color] ✅ 

- 01.02.03各[color=#FF0000]一百五[/color] ❌(105? 150?)[color=#FF0000]系统不能判断[/color]
  01.02.03各[color=#FF0000]一百五十元[/color] ✅ 所以金额要用标准写法

### 有错别字和缺少分隔符号:
- [color=#FF0000]免[/color]龙牛各100元 ❌([color=#FF0000]免[/color]? 应该是[color=#FF0000]兔[/color]吧)
  [color=#FF0000]兔[/color]龙牛各100元 ✅

- 龙[color=#FF0000]候[/color]羊各二百元 ❌([color=#FF0000]候[/color]? 应该是[color=#FF0000]猴[/color]吧 )
  龙[color=#FF0000]猴[/color]羊各二百元 ✅

- 18.[color=#FF0000]2025[/color].10各10元 ❌ 缺少分隔符号
  18.[color=#FF0000]20.25[/color].10各10元 ✅ 在关键地方添加分隔符号

- 18.01.10[color=#FF0000]各 。10元[/color] ❌(各后面不能有[color=#FF0000]任何分隔符号和空格[/color])
  18.01.10[color=#FF0000]各10元[/color] ✅ 删除了多余的分隔符号和空格

# 平特.特肖类标准输入格式
### 单个平特:
- 平特猴500元 或 
  平特一肖猴各五百元 (有无各都行)
### 多个平特:
- 平特猴马牛羊鸡五百元 或 
  平特一肖猴马牛羊鸡各500元 (注意 多个平特的输入[color=#FF0000]不需要分隔符号[/color])
### 特肖和平特一样写法
### 单个平特尾:
- 平特8尾十元 或 
  平特8尾各10元 (这里各可有可无) 
### 多个平特尾:
平特0123456789尾500元 或 
平特0123456789尾各五百元 (注意 多个平特尾的输入[color=#FF0000]不需要分隔符号[/color])

# 连肖类标准输入格式
### 单个连肖:
- 二连肖牛马500元
  二连牛马各五百元
  有无“各”都可以 但不能有分隔符号
- 二连肖[color=#FF0000]牛，马[/color]各500元 ❌ [color=#FF0000]不能有分隔符号[/color]
  二连肖[color=#FF0000]牛马[/color]各500元 ✅
### 多个连肖:
- 二连肖牛马,猪羊,牛蛇500元 或者
  二连肖牛马,猪羊,牛蛇各五百元
  有无“各”都可以 但要分隔符号区分每一组
  二连肖[color=#FF0000]牛马猪羊牛蛇[/color]各500元 ❌ [color=#FF0000]会解析为复式[/color] 
  二连肖[color=#FF0000]牛马,猪羊,牛蛇[/color]各500元 ✅ 
  二连肖,[color=#FF0000]牛马,[/color]各500元 